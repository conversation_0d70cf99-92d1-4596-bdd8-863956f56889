<!-- 个人信息编辑弹窗组件 -->
<view wx:if="{{show}}" class="profile-edit-modal">
  <view class="modal-mask" bindtap="closeModal"></view>
  <view class="modal-content">
    <!-- 弹窗头部 -->
    <view class="modal-header">
      <text class="modal-title">编辑个人信息</text>
      <text class="modal-close" bindtap="closeModal">×</text>
    </view>

    <!-- 表单内容 -->
    <view class="modal-body">
      <!-- 头像编辑 -->
      <view class="form-section">
        <view class="section-label">头像</view>
        <view class="avatar-edit-wrapper">
          <view class="avatar-display">
            <view class="avatar-container" bindtap="chooseAvatar">
              <image
                class="avatar-image"
                src="{{formData.avatar || userInfo.avatar || defaultAvatar}}"
                mode="aspectFill"
              ></image>
            </view>
          </view>
          <view class="avatar-actions">
            <button class="avatar-edit-btn" bindtap="chooseAvatar">
              <text class="btn-text">更换头像</text>
            </button>
          </view>
        </view>
      </view>

      <!-- 昵称编辑 -->
      <view class="form-section">
        <view class="section-label">昵称</view>
        <view class="input-wrapper">
          <input 
            class="form-input" 
            value="{{formData.nickname}}" 
            placeholder="请输入昵称" 
            maxlength="12"
            bindinput="onNicknameInput"
          />
          <view class="char-count">{{formData.nickname.length}}/12</view>
        </view>
        <view wx:if="{{nicknameError}}" class="error-tip">{{nicknameError}}</view>
      </view>

      <!-- 手机号显示（不可编辑） -->
      <view class="form-section">
        <view class="section-label">手机号</view>
        <view class="readonly-field">
          <text class="readonly-text">{{userInfo.phone || '未绑定'}}</text>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="modal-footer">
      <button class="btn btn-cancel" bindtap="closeModal">取消</button>
      <button 
        class="btn btn-confirm {{loading ? 'loading' : ''}}" 
        bindtap="confirmEdit"
        disabled="{{loading}}"
      >
        {{loading ? '保存中...' : '保存'}}
      </button>
    </view>
  </view>
</view>
