// pages/mine/index.js
import siteinfo from '../../siteinfo.js';
import userApi from '../../api/modules/user';
import employeePromotionApi from '../../api/modules/employeePromotion';
import utils from '../utils/util';
import { OrderStatus } from '../../common/constant.js';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    siteinfo,
    userInfo: null,
    pets: [],
    showModal: false,
    modalTitle: '敬请期待',
    modalContent: '该功能正在开发中，我们正努力为您带来更好的体验！',
    // 推广码相关
    showPromotionModal: false,
    promotionCode: '',
    promotionCodeInfo: null,
    isCheckingCode: false,
    promotionEmployee: null, // 关联的推广员工
    // 个人信息编辑相关
    showProfileEditModal: false,
  },
  onShow() {
    wx.hideLoading();
    this.getPets();
    this.getPromotionEmployee();
  },

  onShareAppMessage: function () {
    return {
      title: '贝宠约洗，足不出户享精致洗护～',
      path: `/pages/index/index?aUserId=${this.data?.userInfo?.id || ''}&shareCode=${
        this.data?.userInfo?.promotionCode || ''
      }&shareTime=${Date.now()}`,
      imageUrl: 'https://xian7.zos.ctyun.cn/pet/static/share.png',
      // imageUrl: '/assets/share.png' // 这里填写图片的相对路径或绝对路径
    };
  },
  onShareTimeline: function () {
    return {
      title: '「忙到没空送洗？上门服务解忧！」贝宠约洗，专业到家超省心～',
      imageUrl: 'https://xian7.zos.ctyun.cn/pet/static/share1.png',
      query: `aUserId=${this.data?.userInfo?.id || ''}&shareCode=${
        this.data?.userInfo?.promotionCode || ''
      }&shareTime=${Date.now()}`,
      // imageUrl: '/assets/share.png' // 这里填写图片的相对路径或绝对路径
    };
  },

  uploadAvatar() {
    this.uploadImage(
      this,
      'userInfo.avatar', // 想把url存入哪个字段
      `avatar/`, // 上传的key前缀,例如avatar/
      1
    ).then(res => {
      this.setData({
        'userInfo.avatar': res[0],
      });
    });
  },

  // 显示个人信息编辑弹窗
  showProfileEditModal() {
    const userInfo = this.data.userInfo;
    if (!userInfo) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    this.setData({
      showProfileEditModal: true
    });
  },

  // 隐藏个人信息编辑弹窗
  hideProfileEditModal() {
    this.setData({
      showProfileEditModal: false
    });
  },

  // 个人信息编辑确认
  async onProfileEditConfirm(e) {
    const { updatedData } = e.detail;

    try {
      // 更新本地用户信息
      const userInfo = { ...this.data.userInfo };

      if (updatedData.nickname) {
        userInfo.nickname = updatedData.nickname;
      }

      if (updatedData.avatar) {
        userInfo.avatar = updatedData.avatar;
      }

      this.setData({
        userInfo: userInfo,
        showProfileEditModal: false
      });

      // 更新本地存储的用户信息
      wx.setStorageSync('userInfo', userInfo);

    } catch (error) {
      console.error('更新本地用户信息失败:', error);
    }
  },

  // 获取关联的推广员工
  async getPromotionEmployee() {
    const userInfo = this.data.userInfo;
    if (!userInfo || !userInfo.id) return;

    try {
      const result = await employeePromotionApi.getEmployee(userInfo.id);
      this.setData({
        promotionEmployee: result
      });
    } catch (error) {
      console.error('获取推广员工信息失败:', error);
    }
  },

  // 显示推广码填写弹窗
  showPromotionCodeModal() {
    // 检查是否已有推广关系
    if (this.data.promotionEmployee) {
      wx.showToast({
        title: '您已有关联的推广员工',
        icon: 'none'
      });
      return;
    }

    this.setData({
      showPromotionModal: true,
      promotionCode: '',
      promotionCodeInfo: null
    });
  },

  // 隐藏推广码填写弹窗
  hidePromotionCodeModal() {
    this.setData({
      showPromotionModal: false,
      promotionCode: '',
      promotionCodeInfo: null
    });
  },

  // 推广码输入
  onPromotionCodeInput(e) {
    const code = e.detail.code;
    this.setData({
      promotionCode: code,
      promotionCodeInfo: null
    });

    // 如果推广码不为空，进行验证
    if (code) {
      this.checkPromotionCode(code);
    }
  },

  // 验证推广码
  async checkPromotionCode(code) {
    if (this.data.isCheckingCode) return;

    this.setData({ isCheckingCode: true });

    try {
      const result = await employeePromotionApi.checkCode(code);
      this.setData({
        promotionCodeInfo: result
      });
    } catch (error) {
      console.error('验证推广码失败:', error);
      this.setData({
        promotionCodeInfo: {
          available: false,
          message: '推广码验证失败，请稍后重试'
        }
      });
    } finally {
      this.setData({ isCheckingCode: false });
    }
  },

  // 提交推广码
  async submitPromotionCode(e) {
    const { promotionCode } = e.detail;
    const { userInfo } = this.data;

    try {
      wx.showLoading({ title: '提交中...' });

      await employeePromotionApi.create({
        customerId: userInfo.id,
        promotionCode: promotionCode
      });

      wx.hideLoading();
      wx.showToast({
        title: '推广关系建立成功',
        icon: 'success'
      });

      // 关闭弹窗并刷新推广员工信息
      this.hidePromotionCodeModal();
      this.getPromotionEmployee();

    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: error.message || '提交失败，请稍后重试',
        icon: 'none'
      });
    }
  },
  redirect(evt) {
    let { type } = evt.currentTarget.dataset;
    let url;
    const thiz = this;
    const userInfo = thiz.data.userInfo;
    if (!userInfo) {
      wx.navigateTo({
        url: '/pages/login/index',
      });
      return;
    }
    if (type === 'complaint') {
      url = '/pages/complaint/index';
    }
    if (type === 'clearstorge') {
      wx.showModal({
        title: '清理缓存',
        content: '您的登录信息也将会同步清除，确定要清理吗？',
        success: res => {
          if (res.confirm) {
            wx.clearStorage();
            wx.clearStorageSync();
            wx.showToast({
              title: '缓存清理完成',
              duration: 3000,
            }).then(() => {
              setTimeout(() => {
                wx.redirectTo({
                  url: '/pages/index/index',
                });
              }, 1000);
            });
          }
        },
      });
      return;
    }
    switch (type) {
      case 'serviceAll':
        url = '/pages/serviceOrder/index';
        break;
      case OrderStatus.待付款:
      case OrderStatus.待接单:
      case [OrderStatus.待服务, OrderStatus.已出发, OrderStatus.服务中, OrderStatus.退款中].join(','):
      case [OrderStatus.已完成, OrderStatus.已退款, OrderStatus.已评价].join(','):
        url = '/pages/serviceOrder/index?type=' + type;
        break;
      case 'adress':
        url = '/pages/service/adress/index';
        break;
      case 'user':
        url = '/pages/mine/userAgreement/index';
        break;
      case 'previate':
        url = '/pages/mine/privacyAgreement/index';
        break;
      case 'pet':
        url = '/pages/service/pet/index';
        break;
      case 'vip':
        url = '/pages/mine/rightsCenter/rightsCenter';
        break;
      case 'ticket':
        url = '/pages/mine/myTickets/myTickets';
        break;
      case 'login':
        url = '/pages/login/index';
        break;
      default:
        break;
    }
    if (url) {
      thiz.navigateTo({
        type: 'page',
        url,
        curTab: type === 'serviceAll' ? 'all' : type,
      });
    } else {
      // 显示自定义模态框
      this.setData({
        showModal: true,
        modalTitle: '敬请期待',
        modalContent: '该功能正在开发中，我们正努力为您带来更好的体验！',
      });
    }
  },
  async getPets() {
    const userInfo = this.data.userInfo;
    if (!userInfo) {
      this.setData({
        pets: [],
      });
      return;
    }

    try {
      const data = await userApi.getPets(userInfo.id);

      // 并行获取每个宠物的最后洗护时间
      const formattedData = await Promise.all(data.map(async item => {
        // 格式化年龄
        if (typeof item.bri === 'number' && !isNaN(item.bri)) {
          item.formattedBri = utils.formatAge(item.bri);
        } else {
          item.formattedBri = '无效年龄';
        }

        // 获取最后洗护时间
        try {
          const lastServiceData = await userApi.getLastServiceTime(userInfo.id, item.id);
          item.lastServiceTime = lastServiceData.lastServiceTime;
          item.formattedLastServiceTime = utils.formatLastServiceTime(lastServiceData.lastServiceTime);
        } catch (error) {
          console.log(`获取宠物${item.name}最后洗护时间失败:`, error);
          item.lastServiceTime = null;
          item.formattedLastServiceTime = '暂无洗护记录';
        }

        return item;
      }));

      this.setData({
        pets: formattedData,
      });
    } catch (err) {
      console.log(err);
    }
  },
  // 模态框确认事件
  onModalConfirm() {
    this.setData({
      showModal: false,
    });
  },

  /**
   * 查看宠物详情
   */
  viewPetDetail(event) {
    const item = event.currentTarget.dataset.item;
    console.log('个人中心点击宠物详情:', item);

    if (!item || !item.id) {
      wx.showToast({
        title: '宠物信息错误',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/service/pet/detail?petId=${item.id}`,
      fail: (err) => {
        console.error('页面跳转失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },
});
