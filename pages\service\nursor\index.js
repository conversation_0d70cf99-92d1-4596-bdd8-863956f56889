// pages/service/nursor/index.js
import employeeApi from '../../../api/modules/employee';
import dictionaryApi from '../../../api/modules/dictionary';
import { getVehicleTypeNameFromEmpolyee } from '../../../tools/calc';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    list: [],
    loading: true,
    serviceTypeId: null, // 服务类型ID
    current: 1,
    pageSize: 10,
    total: 0,
    hasMore: true,
    positionDictionary: [], // 员工职位字典
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取传入的服务类型ID
    if (options.serviceTypeId) {
      this.setData({
        serviceTypeId: options.serviceTypeId,
      });
    }
    // 先加载职位字典，再加载员工列表
    this.loadPositionDictionary();
  },

  /**
   * 加载员工职位字典和车辆类型字典
   */
  async loadPositionDictionary() {
    try {
      // 并行加载职位字典和车辆类型字典
      const [positionList] = await Promise.all([dictionaryApi.list('员工职位')]);

      this.setData({
        positionDictionary: positionList || [],
      });
      console.log('字典加载成功:', { positionList });
    } catch (error) {
      console.error('加载字典失败:', error);
      this.setData({
        positionDictionary: [],
      });
    }
    // 加载完字典后再加载员工列表
    this.loadEmployeeList();
  },

  /**
   * 根据职位代码获取职位名称
   */
  getPositionName(positionCode) {
    const { positionDictionary } = this.data;
    if (!positionCode || !positionDictionary.length) {
      return ''; // 职位未设定时不显示默认信息
    }

    const position = positionDictionary.find(item => item.code === positionCode);
    return position ? position.name : '';
  },

  /**
   * 将月数转换为年月格式
   */
  formatWorkExperience(months) {
    if (!months || months <= 0) {
      return '';
    }

    const years = Math.floor(months / 12);
    const remainingMonths = months % 12;

    if (years === 0) {
      return `${remainingMonths}个月经验`;
    } else if (remainingMonths === 0) {
      return `${years}年经验`;
    } else {
      return `${years}年${remainingMonths}个月经验`;
    }
  },

  /**
   * 加载员工列表
   */
  async loadEmployeeList(refresh = false) {
    // 使用页面扩展自动注入的用户信息，如果没有则使用session获取
    const userInfo = this.data.userInfo || this.$session?.getUser();
    if (!userInfo) {
      wx.showToast({
        title: '请先登录',
        icon: 'none',
      });
      wx.navigateTo({
        url: '/pages/login/index',
      });
      return;
    }

    if (refresh) {
      this.setData({
        current: 1,
        list: [],
        hasMore: true,
      });
    }

    const { current, pageSize, serviceTypeId } = this.data;

    try {
      this.setData({ loading: true });

      const params = {
        current,
        pageSize,
      };

      if (serviceTypeId) {
        params.serviceTypeId = serviceTypeId;
      }

      const result = await employeeApi.getList(userInfo.id, params);

      if (result && result.list) {
        // 为每个员工添加翻译后的信息
        const processedList = result.list.map(employee => {
          const processedEmployee = {
            ...employee,
            positionName: this.getPositionName(employee.position),
            formattedWorkExp: this.formatWorkExperience(employee.workExp),
          };

          // 处理车辆信息
          if (employee.vehicle) {
            processedEmployee.vehicle = {
              ...employee.vehicle,
              vehicleTypeName: getVehicleTypeNameFromEmpolyee(processedEmployee.positionName)
            };
          }

          // 根据员工职位设置可服务类型
          if (processedEmployee.positionName === '美容师') {
            // 美容师可以接洗护和美容服务
            processedEmployee.serviceTypeNames = ['洗护', '美容'];
          } else if (processedEmployee.positionName === '洗护师') {
            // 洗护师只能接洗护服务
            processedEmployee.serviceTypeNames = ['洗护'];
          } else {
            // 其他职位默认可以接所有类型
            processedEmployee.serviceTypeNames = [];
          }

          return processedEmployee;
        });

        const newList = refresh ? processedList : [...this.data.list, ...processedList];

        this.setData({
          list: newList,
          total: result.total,
          current: current + 1,
          hasMore: newList.length < result.total,
          loading: false,
        });
      }
    } catch (error) {
      console.error('加载员工列表失败:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none',
      });
      this.setData({ loading: false });
    }
  },

  /**
   * 选择员工并跳转到服务页面
   */
  redirect(evt) {
    const { employee } = evt.currentTarget.dataset;

    if (!employee) {
      wx.showToast({
        title: '员工信息错误',
        icon: 'none',
      });
      return;
    }

    // 将选中的员工信息存储到本地，供后续下单使用
    wx.setStorageSync('selectedEmployee', employee);

    // 跳转到服务页面，传递员工ID
    wx.navigateTo({
      url: `/pages/service/index?employeeId=${employee.id}`,
    });
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadEmployeeList(true).finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadEmployeeList();
    }
  },
});
