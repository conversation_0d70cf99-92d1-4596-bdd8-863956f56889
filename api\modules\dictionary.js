import session from "../../common/Session";
import request, { analysisRes } from "../request";
import config from "../config";
import requestManager from "../../utils/requestManager.js";

const DICTIONARY_KEY = "dictionary_com";
const { dictionary } = config.apiUrls;
export default {
  // list: "/openapi/dictionary", // 查询字典列表
  // type可选，不传则查询所有字典
  async list(type) {
    return requestManager.request(
      dictionary.list,
      { type: type || 'all' },
      async () => {
        // 获取缓存数据和缓存时间
        let cacheData = session.getValue(DICTIONARY_KEY);
        let cacheTime = session.getValue(DICTIONARY_KEY + "_time");
        const currentTime = new Date().getTime();
        const oneHour = 60 * 60 * 1000; // 1小时的毫秒数

        // 检查缓存是否存在、是否为空、是否超过1小时
        const isCacheValid = cacheData &&
                             cacheData.length > 0 &&
                             cacheTime &&
                             (currentTime - cacheTime < oneHour);

        // 如果缓存无效，则重新请求数据
        if (!isCacheValid) {
          const res = await request.get(dictionary.list);
          cacheData = analysisRes(res);

          // 更新缓存数据和缓存时间
          session.setValue(DICTIONARY_KEY, cacheData);
          session.setValue(DICTIONARY_KEY + "_time", currentTime);
          console.log("已更新字典缓存，时间：", new Date().toLocaleString());
        }

        // 根据type参数过滤并返回数据
        if (type) {
          return cacheData.filter((item) => item.type == type);
        } else {
          return cacheData;
        }
      },
      { cacheTime: 5000 } // 短期缓存5秒，防止重复请求
    );
  },
};
