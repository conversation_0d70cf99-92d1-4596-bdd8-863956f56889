/**
 * 将列表转换为树形结构
 *
 * @param {Object} params 参数
 * @param {Array} param.list 列表
 * @param {String} [param.parentKey] 父级key，不传递为根节点
 * @param {String} [param.childKey] 返回数据的子级key，默认为children
 * @param {{parent: string; child: string}} [param.fieldName] 定义父子关系的字段名称，默认为{parent: 'code', child: 'parentCode'}
 * @returns {Object[]} any[]
 */
export const convertListToTree = ({
  list,
  parentKey,
  childKey = "children",
  fieldName = {
    parent: "code",
    child: "parentCode",
  },
}) => {
  const tree = [];
  const map = new Map();
  list.forEach((item) => {
    map.set(item[fieldName.parent], { ...item, [childKey]: [] });
  });
  list.forEach((item) => {
    const parent = map.get(item[fieldName.child]);
    if (parent) {
      parent[childKey].push(map.get(item[fieldName.parent]));
    } else if (!parentKey || !item[fieldName.child]) {
      tree.push(map.get(item[fieldName.parent]));
    }
  });
  // 删除children长度为0的节点
  const removeEmptyChildren = (node) => {
    if (!node) {
      console.warn("node is null");
      return;
    }
    if (node[childKey] && node[childKey].length === 0) {
      delete node[childKey];
    } else if (node[childKey]) {
      node[childKey].forEach((child) => removeEmptyChildren(child));
    }
  };
  tree.forEach((node) => removeEmptyChildren(node));
  return tree;
};

/**
 * 从员工职位信息中获取车辆类型名称
 * @param {string} positionName 员工职位名称
 * @returns {String} 车辆类型名称
 */
export const getVehicleTypeNameFromEmpolyee = (positionName) => {
  if (!positionName) {
    return '';
  }
  return positionName.replace('师', '');
};
