<view class="container">
  <view class="content">
    <view wx:if="{{loading}}" class="loading-container">
      <text>加载中...</text>
    </view>

    <view wx:elif="{{messageDetail}}" class="message-detail">
      <!-- 消息头部信息 -->
      <view class="message-header {{messageDetail.isRead ? 'read' : 'unread'}}">
        <view class="message-info">
          <image src="{{messageDetail.icon}}" class="message-icon" mode="widthFix"></image>
          <view class="message-meta">
            <view class="message-time">{{messageDetail.time}}</view>
            <view wx:if="{{messageDetail.isRead}}" class="read-status">已读</view>
          </view>
        </view>
      </view>

      <!-- 消息内容 -->
      <view class="message-content">
        <text class="content-text">{{messageDetail.content}}</text>
      </view>

      <!-- 额外数据展示 -->
      <view wx:if="{{messageDetail.parsedExtraData}}" class="message-extra">
        <view class="extra-title">相关信息</view>
        <view wx:for="{{messageDetail.parsedExtraData}}" wx:key="label" class="extra-item">
          <text class="extra-label">{{item.label}}：</text>
          <text class="extra-value">{{item.value}}</text>
        </view>
      </view>
    </view>

    <view wx:else class="error-container">
      <empty icon="//xian7.zos.ctyun.cn/pet/static/nomessage.png" text="消息不存在或已被删除" />
    </view>
  </view>
</view>
