import orderApi from '../../../api/modules/order.js';
import payApi from '../../../api/modules/pay';

import utils from '../../utils/util';
import WeMessage from '../../../common/WeMessage.js';
import Session from '../../../common/Session.js';
import { OrderStatus } from '../../../common/constant.js';
import additionalServiceApi from '../../../api/modules/additionalService.js';

Page({
  data: {
    userInfo: null,
    orderDetail: {}, // 订单
    servicePhotos: null, // 服务照片

    additionalServices: [], // 追加服务列表
    showMoreActions: false,
    showModal: false,
    modalTitle: '敬请期待',
    modalContent: '支付功能稍后上线！',
    modalButtons: [],
    clickEvent: () => {
      // 返回上一页
      wx.navigateBack({
        delta: 1,
      });
    },

    // 追加服务支付相关
    paying: false,
    currentPayService: null, // 当前要支付的追加服务

    // 地址修改相关
    showAddressModal: false,
    addressModalData: {}, // 传递给地址修改组件的初始数据

    // 时间修改相关
    showTimeModal: false,
    timeModalData: {}, // 传递给时间修改组件的初始数据

    // 待执行的动作
    pendingAction: null,

    // 追加服务状态映射
    additionalServiceStatusMap: {
      pending_confirm: {
        text: '待确认',
        color: '#ff9500',
        desc: '等待员工确认',
        actions: ['view', 'delete']
      },
      confirmed: {
        text: '已确认',
        color: '#007aff',
        desc: '请尽快完成支付',
        actions: ['pay', 'view', 'delete']
      },
      rejected: {
        text: '已拒绝',
        color: '#ff3b30',
        desc: '申请被拒绝',
        actions: ['view', 'delete']
      },
      pending_payment: {
        text: '待付款',
        color: '#ff9500',
        desc: '请尽快完成支付',
        actions: ['pay', 'view']
      },
      paid: {
        text: '已付款',
        color: '#34c759',
        desc: '服务进行中',
        actions: ['view']
      },
      completed: {
        text: '已完成',
        color: '#34c759',
        desc: '服务已完成',
        actions: ['view']
      },
      cancelled: {
        text: '已取消',
        color: '#8e8e93',
        desc: '服务已取消',
        actions: ['view']
      },
      refunding: {
        text: '退款中',
        color: '#ff9500',
        desc: '退款处理中',
        actions: ['view']
      },
      refunded: {
        text: '已退款',
        color: '#8e8e93',
        desc: '退款已完成',
        actions: ['view']
      }
    },
  },

  /**
   * 判断是否可以给员工打电话
   * 订单被接单后（待服务、已出发、服务中）到订单结束前可以打电话
   */
  canCallEmployee() {
    const { orderDetail } = this.data;
    if (!orderDetail || !orderDetail.status) {
      return false;
    }

    const callableStatuses = [
      OrderStatus.待服务,
      OrderStatus.已出发,
      OrderStatus.服务中
    ];

    return callableStatuses.includes(orderDetail.status);
  },

  /**
   * 判断是否应该加载追加服务
   * 从服务开始到订单完成期间都可能有追加服务
   */
  shouldLoadAdditionalServices(orderStatus) {
    if (!orderStatus) {
      return false;
    }

    // 从待服务开始到已评价结束，都可能有追加服务
    const statusesWithAdditionalServices = [
      OrderStatus.待服务,
      OrderStatus.已出发,
      OrderStatus.服务中,
      OrderStatus.已完成,
      OrderStatus.已评价
    ];

    return statusesWithAdditionalServices.includes(orderStatus);
  },

  /**
   * 拨打员工电话
   */
  callEmployee() {
    const { orderDetail } = this.data;

    if (!this.canCallEmployee()) {
      wx.showToast({
        title: '当前订单状态不支持联系员工',
        icon: 'none'
      });
      return;
    }

    if (!orderDetail.employee || !orderDetail.employee.phone) {
      wx.showToast({
        title: '员工联系方式不可用',
        icon: 'none'
      });
      return;
    }

    wx.makePhoneCall({
      phoneNumber: orderDetail.employee.phone,
      success: () => {
        console.log('成功发起电话呼叫');
      },
      fail: (err) => {
        console.error('电话呼叫失败', err);
        wx.showToast({
          title: '拨打电话失败',
          icon: 'none'
        });
      }
    });
  },

  onLoad(options) {
    const { data, actionType } = options;
    // 使用页面扩展的用户信息获取方式，如果没有则使用session获取
    const userInfo = this.data.userInfo || this.$session?.getUser() || Session.getUser();

    this.setData({
      userInfo,
      pendingAction: actionType // 保存待执行的动作
    });

    // 加载初始订单数据
    this.loadOrders(data);
  },
  onShow() {
    // 确保用户信息是最新的
    const userInfo = this.data.userInfo || this.$session?.getUser() || Session.getUser();
    if (userInfo && !this.data.userInfo) {
      this.setData({ userInfo });
    }

    // 如果订单有追加服务的可能，刷新追加服务列表
    const { orderDetail } = this.data;
    if (orderDetail && this.shouldLoadAdditionalServices(orderDetail.status) && orderDetail.orderDetails && orderDetail.orderDetails.length > 0) {
      // 添加小延迟确保从申请页面返回时能获取到最新数据
      setTimeout(() => {
        this.loadAdditionalServices(orderDetail.orderDetails[0].id);
      }, 100);
    }

    // 更新是否可以打电话的状态和地址修改按钮状态
    if (orderDetail) {
      this.setData({
        canCallEmployee: this.canCallEmployee(),
        canModifyAddress: this.canModifyServiceAddress(),
        canModifyTime: this.canModifyServiceTime()
      });
    }
  },
  // 加载订单数据
  loadOrders(data) {
    wx.showLoading({
      title: '加载中',
    });

    const mockOrder = JSON.parse(data);
    const { serviceTime, orderTime, createdAt, ...rest } = mockOrder;
    const orderDetail = {
      ...rest,
      serviceTime: serviceTime ? utils.formatNormalDate(serviceTime) : null,
      // 下单时间
      orderTime: orderTime ? utils.formatNormalDate(orderTime) : null,
      // createdAt: createdAt ? utils.formatNormalDate(createdAt) : null,
    };

    this.setData({
      orderDetail,
      canCallEmployee: this.canCallEmployee(),
      canModifyAddress: this.canModifyServiceAddress(),
      canModifyTime: this.canModifyServiceTime()
    });

    // 如果订单状态是服务中、已完成或已评价，加载服务照片
    if (orderDetail.status === OrderStatus.服务中 ||
        orderDetail.status === OrderStatus.已完成 ||
        orderDetail.status === OrderStatus.已评价) {
      this.loadServicePhotos(orderDetail.id);
    }

    // 如果订单有追加服务的可能，加载追加服务
    if (this.shouldLoadAdditionalServices(orderDetail.status) && orderDetail.orderDetails && orderDetail.orderDetails.length > 0) {
      this.loadAdditionalServices(orderDetail.orderDetails[0].id);
    }

    wx.hideLoading();

    // 执行待执行的动作
    this.executePendingAction();
  },

  /**
   * 执行待执行的动作
   */
  executePendingAction() {
    const { pendingAction } = this.data;

    if (pendingAction === 'modifyAddress') {
      // 延迟一点时间确保页面渲染完成
      setTimeout(() => {
        this.modifyServiceAddress();
        // 清除待执行的动作
        this.setData({ pendingAction: null });
      }, 300);
    } else if (pendingAction === 'modifyTime') {
      // 延迟一点时间确保页面渲染完成
      setTimeout(() => {
        this.modifyServiceTime();
        // 清除待执行的动作
        this.setData({ pendingAction: null });
      }, 300);
    }
  },

  // 删除订单
  cancelOrder(e) {
    const orderId = e.currentTarget.dataset.orderId;
    wx.showModal({
      title: '取消订单',
      content: '确定要取消此订单吗？',
      success: res => {
        if (res.confirm) {
          const userInfo = this.data.userInfo;
          orderApi.cancel(userInfo.id, orderId).then(res => {
            if (res) {
              wx.showToast({
                title: '取消成功',
                icon: 'success',
              });
              wx.navigateBack({
                delta: 1,
              });
            }
          });
        }
      },
    });
  },
  // 切换更多操作弹窗
  toggleOrderActions(e) {
    this.setData({
      showMoreActions: !this.data.showMoreActions,
    });
  },
  // 支付订单
  payOrder(e) {
    const _this = this;
    const userInfo = this.data.userInfo;
    if (!userInfo) {
      this.navigateTo({
        type: 'tip',
        tip: '获取订单信息失败，请重新登录',
      });
      return;
    }
    const sn = e.currentTarget.dataset.sn;
    const order = this.data.orderDetail;

    if (!order) {
      wx.showToast({
        title: '订单信息不存在',
        icon: 'none',
      });
      return;
    }

    if (Number(order.amount) === 0) {
      wx.showLoading({
        title: '处理中',
      });
      // 使用统一的0元订单处理方法
      orderApi.handleZeroAmountOrder(userInfo.id, sn).then(res => {
        wx.hideLoading();
        if (res.success) {
          // 显示自定义模态框
          // _this.setData({
          //   modal: {
          //     isShow: false,
          //   },
          //   showModal: true,
          //   modalTitle: '提交成功',
          //   modalContent: '护理师会尽快和您联系，请耐心等待',
          // });
          // 刷新订单详情
          _this.loadOrders(sn);
          console.log('6----', userInfo);
          _this.handlePaymentSuccessModal(userInfo.openid, order.id);
        } else {
          wx.showToast({
            title: res.error || '处理失败，请稍后重试',
            icon: 'none',
          });
        }
      });
      return;
    }

    // 支付
    payApi.doPay({
      sn,
      onOk: () => {
        // 支付成功后，调用支付成功接口更新订单状态
        orderApi.pay(userInfo.id, sn).then(res => {
          // 显示自定义模态框
          // _this.setData({
          //   modal: {
          //     isShow: false,
          //   },
          //   showModal: true,
          //   modalTitle: '提交成功',
          //   modalContent: '护理师会尽快和您联系，请耐心等待',
          // });
          // 刷新订单详情
          _this.loadOrders(sn);
          console.log('7----', userInfo);
          _this.handlePaymentSuccessModal(userInfo.openid, order.id);
        });
      },
      onCancel: () => {
        wx.showToast({
          title: '取消支付',
          icon: 'none',
        });
      },
      onError: () => {
        wx.showToast({
          title: '支付失败',
          icon: 'none',
        });
      },
      complete: () => {},
    });
  },
  /**
   * 处理支付成功后的弹窗 - 订单详情页面
   * @param {string} openId 用户openId
   * @param {string} sn 订单编号
   */
  handlePaymentSuccessModal(openId, sn) {
    const weMessage = new WeMessage(openId, sn, OrderStatus.待接单);
    const modalConfig = weMessage.handlePaymentSuccess();
    if (modalConfig) {
      this.setData({
        showModal: true,
        modalTitle: modalConfig.modalConfig.title,
        modalContent: modalConfig.modalConfig.content,
        modalButtons: modalConfig.modalConfig.buttons,
      });
      this._weMessage = weMessage;
    }
  },
  // 处理订阅确认按钮点击
  handleModalConfirm(e) {
    if (this._weMessage) {
      this._weMessage.requestOrderConfirmationSubscribe();
    }
    this.setData({ showModal: false });
    this.data.clickEvent && this.data.clickEvent();
  },
  // 处理订阅取消按钮点击
  handleModalCancel(e) {
    if (this._weMessage) {
      this._weMessage.recordUserChoice(false);
    }
    this.setData({ showModal: false });
    this.data.clickEvent && this.data.clickEvent();
  },

  // 加载服务照片
  async loadServicePhotos(orderId) {
    try {
      const servicePhotos = await orderApi.getServicePhotos(orderId);
      if (servicePhotos) {
        // 格式化时间
        const formattedPhotos = {
          ...servicePhotos,
          beforePhotoTime: servicePhotos.beforePhotoTime ? utils.formatNormalDate(servicePhotos.beforePhotoTime) : null,
          afterPhotoTime: servicePhotos.afterPhotoTime ? utils.formatNormalDate(servicePhotos.afterPhotoTime) : null,
        };
        this.setData({ servicePhotos: formattedPhotos });
      }
    } catch (error) {
      console.error('加载服务照片失败:', error);
      // 不显示错误提示，因为没有照片是正常情况
    }
  },

  // 预览服务照片 - 组件事件处理
  previewServicePhoto(e) {
    const { url, type, photos } = e.detail;

    if (!url || !type || !photos || photos.length === 0) return;

    wx.previewImage({
      current: url,
      urls: photos
    });
  },





  // 申请追加服务
  applyAdditionalService(e) {
    const { orderDetailId } = e.currentTarget.dataset;
    const { orderDetail } = this.data;

    if (!orderDetail || orderDetail.status !== OrderStatus.服务中) {
      wx.showToast({
        title: '当前订单状态不支持申请追加服务',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/additionalService/apply/index?orderDetailId=${orderDetailId}`
    });
  },

  // 加载追加服务列表
  async loadAdditionalServices(orderDetailId) {
    try {
      // 检查orderDetailId是否有效
      if (!orderDetailId) {
        this.setData({ additionalServices: [] });
        return;
      }

      const services = await additionalServiceApi.list(orderDetailId);

      // 处理API返回的数据结构
      let servicesList = [];

      if (services && (services.originalAdditionalServices || services.additionalServiceOrders)) {
        const { originalAdditionalServices = [], additionalServiceOrders = [] } = services;

        // 处理主订单增项服务 - 不显示在追加服务列表中
        const originalServices = [];

        // 处理后续追加的服务订单
        const additionalServices = additionalServiceOrders.map(item => ({
          ...item,
          type: 'additional', // 标记为追加服务
          createdAt: item.createdAt ? utils.formatNormalDate(item.createdAt) : '',
          confirmTime: item.confirmTime ? utils.formatNormalDate(item.confirmTime) : '',
          statusInfo: this.data.additionalServiceStatusMap[item.status] || {
            text: item.status,
            color: '#999',
            desc: '',
            actions: ['view']
          }
        }));

        // 合并两种类型的服务
        servicesList = [...originalServices, ...additionalServices];
      }  else {
        this.setData({ additionalServices: [] });
        return;
      }

      // 格式化数据（对于非原始增项服务）
      const formattedServices = servicesList.map(item => {
        if (item.type === 'original') {
          // 原始增项服务已经格式化过了
          return item;
        }

        return {
          ...item,
          createdAt: item.createdAt ? utils.formatNormalDate(item.createdAt) : '',
          confirmTime: item.confirmTime ? utils.formatNormalDate(item.confirmTime) : '',
          statusInfo: this.data.additionalServiceStatusMap[item.status] || {
            text: item.status,
            color: '#999',
            desc: '',
            actions: ['view']
          }
        };
      });

      this.setData({
        additionalServices: formattedServices
      });

    } catch (error) {
      console.error('加载追加服务列表失败:', error);
      // 设置空数组确保UI状态正确
      this.setData({
        additionalServices: []
      });
    }
  },

  // 查看追加服务详情 - 组件事件处理
  viewAdditionalServiceDetail(e) {
    const { id } = e.detail;
    const { orderDetail } = this.data;

    if (!orderDetail.orderDetails || orderDetail.orderDetails.length === 0) return;

    const orderDetailId = orderDetail.orderDetails[0].id;

    wx.navigateTo({
      url: `/pages/additionalService/detail/index?orderDetailId=${orderDetailId}&id=${id}`
    });
  },

  // 支付追加服务 - 组件事件处理
  payAdditionalService(e) {
    const { id } = e.detail;
    const { additionalServices } = this.data;

    // 找到要支付的服务
    const service = additionalServices.find(item => item.id === parseInt(id));
    if (!service) {
      wx.showToast({
        title: '服务信息不存在',
        icon: 'none',
      });
      return;
    }

    // 检查状态 - 已确认和待付款状态都支持支付
    if (service.status !== 'confirmed' && service.status !== 'pending_payment') {
      wx.showToast({
        title: '当前状态不支持支付',
        icon: 'none',
      });
      return;
    }

    // 设置当前支付服务并显示确认模态框
    this.setData({
      currentPayService: service,
      showModal: true,
      modalTitle: '确认支付',
      modalContent: `确认支付 ¥${service.totalFee} 吗？`,
      modalButtons: [
        { text: '取消', type: 'cancel', event: 'handlePayModalCancel' },
        { text: '确认支付', type: 'primary', event: 'handlePayConfirm' },
      ],
    });
  },

  // 删除追加服务 - 组件事件处理
  deleteAdditionalService(e) {
    const { id, name } = e.detail;

    // 检查是否为原始增项服务
    if (typeof id === 'string' && id.startsWith('original_')) {
      wx.showToast({
        title: '主订单增项服务不能删除',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '删除确认',
      content: `确定要删除追加服务"${name}"吗？`,
      success: (res) => {
        if (res.confirm) {
          this.performDeleteAdditionalService(id);
        }
      }
    });
  },

  // 执行删除追加服务
  async performDeleteAdditionalService(id) {
    try {
      wx.showLoading({ title: '删除中...' });

      const { orderDetail, userInfo } = this.data;
      if (!orderDetail.orderDetails || orderDetail.orderDetails.length === 0) {
        throw new Error('订单详情不存在');
      }

      if (!userInfo || !userInfo.id) {
        throw new Error('用户信息不存在');
      }

      const orderDetailId = orderDetail.orderDetails[0].id;

      // 调用删除API，传递customerId
      await additionalServiceApi.delete(orderDetailId, id, userInfo.id);

      // 重新加载追加服务列表
      await this.loadAdditionalServices(orderDetailId);

      wx.showToast({
        title: '删除成功',
        icon: 'success'
      });

    } catch (error) {
      console.error('删除追加服务失败:', error);

      // 根据错误类型显示不同的提示信息
      let errorMessage = '删除失败';
      if (error.message && error.message.includes('待确认')) {
        errorMessage = '只能删除待确认状态的申请';
      } else if (error.message && error.message.includes('权限')) {
        errorMessage = '无权限删除此申请';
      }

      wx.showToast({
        title: errorMessage,
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 处理追加服务支付确认
   */
  async handlePayConfirm() {
    this.setData({ showModal: false });

    // 先同步支付状态，检查是否已经支付
    const { currentPayService } = this.data;
    if (currentPayService) {
      wx.showLoading({ title: '检查支付状态...' });

      const syncResult = await this.syncAdditionalServicePaymentStatus(currentPayService.id);
      wx.hideLoading();

      if (syncResult) {
        // 同步成功，检查同步结果
        const { additionalServices } = this.data;
        const updatedService = additionalServices.find(item => item.id === currentPayService.id);

        if (updatedService && updatedService.status === 'paid') {
          // 已经是已支付状态，直接显示成功并刷新
          this.setData({ currentPayService: null });
          this.showAdditionalServicePaymentSuccessModal();
          return;
        }
      }
    }

    // 如果未支付或同步失败，继续正常支付流程
    await this.processAdditionalServicePay();
  },

  /**
   * 处理追加服务支付
   */
  async processAdditionalServicePay() {
    const { userInfo, orderDetail, currentPayService } = this.data;

    if (!currentPayService || this.data.paying) return;

    if (!orderDetail.orderDetails || orderDetail.orderDetails.length === 0) {
      wx.showToast({
        title: '订单信息不完整',
        icon: 'none',
      });
      return;
    }

    const orderDetailId = orderDetail.orderDetails[0].id;

    try {
      this.setData({ paying: true });

      // 处理0元订单
      if (Number(currentPayService.totalFee) === 0) {
        wx.showLoading({ title: '处理中...' });
        const result = await additionalServiceApi.pay(orderDetailId, currentPayService.id, userInfo.id);
        wx.hideLoading();

        if (result) {
          // 0元订单支付成功后刷新数据并显示成功
          await this.refreshOrderData();
          this.showAdditionalServicePaymentSuccessModal();
        } else {
          wx.showToast({ title: '支付失败，请重试', icon: 'none' });
        }
        return;
      }

      // 正常支付流程
      wx.showLoading({ title: '发起支付...' });
      const payResult = await additionalServiceApi.pay(orderDetailId, currentPayService.id, userInfo.id);
      wx.hideLoading();

      if (payResult && payResult.orderSn) {
        // 调用微信支付
        const _this = this;
        payApi.doPay({
          sn: payResult.orderSn,
          onOk: async () => {
            // 支付成功后刷新数据并显示成功
            await _this.refreshOrderData();
            _this.showAdditionalServicePaymentSuccessModal();
          },
          onCancel: () => wx.showToast({ title: '取消支付', icon: 'none' }),
          onError: () => wx.showToast({ title: '支付失败', icon: 'none' }),
        });
      } else {
        wx.showToast({ title: '支付失败，请重试', icon: 'none' });
      }

    } catch (error) {
      wx.hideLoading();
      console.error('追加服务支付失败:', error);
      wx.showToast({ title: '支付失败，请重试', icon: 'none' });
    } finally {
      this.setData({ paying: false });
    }
  },

  /**
   * 显示追加服务支付成功模态框
   */
  showAdditionalServicePaymentSuccessModal() {
    this.setData({
      showModal: true,
      modalTitle: '支付成功',
      modalContent: '您的追加服务已支付成功，服务人员将为您提供服务',
      modalButtons: [{ text: '确定', type: 'primary', event: 'handleAdditionalServicePaymentSuccess' }],
    });
  },

  /**
   * 刷新订单数据 - 从服务器重新获取最新数据
   */
  async refreshOrderData() {
    const { orderDetail, userInfo } = this.data;
    if (orderDetail && orderDetail.id && userInfo && userInfo.id) {
      try {
        wx.showLoading({ title: '刷新中...' });

        // 从服务器重新获取订单详情
        const latestOrderDetail = await orderApi.getDetail(userInfo.id, orderDetail.id);

        if (latestOrderDetail) {
          // 使用最新的订单数据重新加载页面
          const orderData = JSON.stringify(latestOrderDetail);
          this.loadOrders(orderData);
        }

        wx.hideLoading();
      } catch (error) {
        wx.hideLoading();
        console.error('刷新订单数据失败:', error);
        // 刷新失败时，仍然重新加载追加服务列表
        if (orderDetail.orderDetails && orderDetail.orderDetails.length > 0) {
          this.loadAdditionalServices(orderDetail.orderDetails[0].id);
        }
      }
    }
  },

  /**
   * 检查是否可以修改服务地址
   */
  canModifyServiceAddress() {
    const { orderDetail } = this.data;
    if (!orderDetail || !orderDetail.status) {
      return false;
    }

    // 只有在待付款、待接单、待服务状态下才能修改服务地址
    const allowedStatuses = [
      OrderStatus.待付款,
      OrderStatus.待接单,
      OrderStatus.待服务
    ];

    return allowedStatuses.includes(orderDetail.status);
  },

  /**
   * 检查是否可以修改服务时间
   */
  canModifyServiceTime() {
    const { orderDetail } = this.data;
    if (!orderDetail || !orderDetail.status) {
      return false;
    }

    // 只有在待付款、待接单、待服务状态下才能修改服务时间
    const allowedStatuses = [
      OrderStatus.待付款,
      OrderStatus.待接单,
      OrderStatus.待服务
    ];

    return allowedStatuses.includes(orderDetail.status);
  },

  /**
   * 修改服务地址
   */
  modifyServiceAddress() {
    const { orderDetail } = this.data;

    if (!this.canModifyServiceAddress()) {
      wx.showToast({
        title: '当前订单状态不允许修改服务地址',
        icon: 'none'
      });
      return;
    }

    // 显示地址修改弹窗
    this.setData({
      showAddressModal: true,
      addressModalData: {
        address: orderDetail.address || '',
        addressDetail: orderDetail.addressDetail || '',
        addressRemark: orderDetail.addressRemark || '',
        longitude: orderDetail.longitude || null,
        latitude: orderDetail.latitude || null,
        addressId: orderDetail.addressId || null
      }
    });
  },

  /**
   * 修改服务时间
   */
  modifyServiceTime() {
    const { orderDetail } = this.data;

    if (!this.canModifyServiceTime()) {
      wx.showToast({
        title: '当前订单状态不允许修改服务时间',
        icon: 'none'
      });
      return;
    }

    // 显示时间修改弹窗
    this.setData({
      showTimeModal: true,
      timeModalData: {
        serviceTime: orderDetail.serviceTime || '',
        orderId: orderDetail.id
      }
    });
  },

  /**
   * 同步追加服务支付状态
   * @param {number} serviceId 追加服务ID
   * @param {boolean} autoRefresh 是否自动刷新数据，默认true
   */
  async syncAdditionalServicePaymentStatus(serviceId, autoRefresh = true) {
    const { orderDetail, userInfo } = this.data;

    if (!orderDetail.orderDetails || orderDetail.orderDetails.length === 0) {
      console.error('订单详情不存在，无法同步支付状态');
      return false;
    }

    const orderDetailId = orderDetail.orderDetails[0].id;

    try {
      console.log('开始同步追加服务支付状态...');
      const syncResult = await additionalServiceApi.syncPaymentStatus(orderDetailId, serviceId, userInfo.id);

      if (syncResult && syncResult.success) {
        console.log('支付状态同步成功:', syncResult.message);

        // 根据参数决定是否自动刷新订单数据
        if (autoRefresh) {
          await this.refreshOrderData();
        }

        return syncResult;
      } else {
        console.log('支付状态同步失败:', syncResult?.message || '未知错误');
        return false;
      }
    } catch (error) {
      console.error('同步支付状态异常:', error);
      return false;
    }
  },

  /**
   * 处理追加服务支付成功
   */
  async handleAdditionalServicePaymentSuccess() {
    this.setData({
      showModal: false,
      currentPayService: null
    });

    // 点击确定按钮后刷新详情页面
    await this.refreshOrderData();
  },

  /**
   * 关闭地址修改弹窗
   */
  closeAddressModal() {
    this.setData({ showAddressModal: false });
  },

  /**
   * 关闭时间修改弹窗
   */
  closeTimeModal() {
    this.setData({ showTimeModal: false });
  },

  /**
   * 处理地址修改确认事件
   */
  async handleAddressModifyConfirm(e) {
    const { addressForm } = e.detail;
    const { orderDetail } = this.data;

    try {
      wx.showLoading({ title: '修改中...' });

      // 准备提交数据
      const submitData = {
        address: addressForm.address.trim(),
        addressDetail: addressForm.addressDetail.trim(),
        longitude: addressForm.longitude,
        latitude: addressForm.latitude,
        addressRemark: addressForm.addressRemark.trim(),
        addressId: addressForm.addressId,
        userType: 'customer'
      };

      // 调试信息：显示提交的数据
      console.log('提交的地址数据:', {
        address: submitData.address,
        addressDetail: submitData.addressDetail,
        longitude: submitData.longitude,
        latitude: submitData.latitude,
        addressId: submitData.addressId,
        hasCoordinates: !!(submitData.longitude && submitData.latitude)
      });

      // 调用修改服务地址API
      await orderApi.updateServiceAddress(orderDetail.id, submitData);

      wx.hideLoading();
      wx.showToast({
        title: '修改成功',
        icon: 'success'
      });

      // 关闭弹窗并刷新数据
      this.setData({ showAddressModal: false });
      await this.refreshOrderData();

    } catch (error) {
      wx.hideLoading();
      console.error('修改服务地址失败:', error);
      wx.showToast({
        title: error.message || '修改失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 处理时间修改确认事件
   */
  async handleTimeModifyConfirm(e) {
    const { timeForm } = e.detail;
    const { orderDetail } = this.data;

    try {
      wx.showLoading({ title: '修改中...' });

      // 调用修改服务时间API
      await orderApi.updateServiceTime(orderDetail.id, new Date(timeForm.serviceTime).toISOString());

      wx.hideLoading();
      wx.showToast({
        title: '修改成功',
        icon: 'success'
      });

      // 关闭弹窗并刷新数据
      this.setData({ showTimeModal: false });
      await this.refreshOrderData();

    } catch (error) {
      wx.hideLoading();
      console.error('修改服务时间失败:', error);
      wx.showToast({
        title: error.message || '修改失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 处理支付模态框取消
   */
  handlePayModalCancel() {
    this.setData({
      showModal: false,
      currentPayService: null
    });
  },

  /**
   * 预览备注图片
   */
  previewRemarkPhoto(e) {
    const url = e.currentTarget.dataset.url;
    const urls = e.currentTarget.dataset.urls;
    wx.previewImage({
      current: url,
      urls: urls
    });
  },
});
