import request, { analysisRes } from "../request";
import config from "../config";
import requestManager from "../../utils/requestManager.js";

const { activity } = config.apiUrls;

export default {
  // 获取当前发布的活动
  async getCurrent(params = {}) {
    return requestManager.request(
      activity.current,
      params,
      async () => {
        const res = await request.get(activity.current, params);
        return analysisRes(res);
      },
      { cacheTime: 60000 } // 缓存1分钟，活动信息变化较少
    );
  },

  // 获取活动详情
  async getDetail(id) {
    const res = await request.get(activity.detail.replace("{id}", id));
    return analysisRes(res);
  },
};
