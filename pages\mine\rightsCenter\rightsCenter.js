// pages/mine/rightsCenter/rightsCenter.js
import rightsCardApi from '../../../api/modules/rightsCard';
import couponApi from '../../../api/modules/coupon';
import payApi from '../../../api/modules/pay';
import userApi from '../../../api/modules/user';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    userInfo: null, // 用户信息
    hasRights_nk: false, // 是否有权益卡年卡
    rightsList_nk: [],
    hasRights_ck: false, // 是否有权益卡次卡
    rightsList_ck: [],
    hasCoupons: false, // 是否有优惠券
    couponsList: [],
    showModal: false, // 是否显示模态框
    modalTitle: '', // 模态框标题
    modalContent: '', // 模态框内容
    currentId: '', // 当前操作的ID
    currentType: '', // 当前操作的类型：rightsCard 或 coupon
  },

  async getRightsList() {
    const res = await rightsCardApi.list();
    const nkList = res?.list?.filter(item => item.type === 'discount') || [];
    const ckList = res?.list?.filter(item => item.type === 'times') || [];
    this.setData({
      rightsList_nk: nkList,
      hasRights_nk: !!nkList.length,
      rightsList_ck: ckList,
      hasRights_ck: !!ckList.length,
    });
  },

  async getCouponList() {
    const res = await couponApi.list();
    this.setData({
      couponsList: (res?.list || []).map(item => ({
        ...item,
        price: Number(item.price),
      })),
      hasCoupons: !!res?.list?.length,
    });
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    this.getRightsList();
    this.getCouponList();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    return {
      title: '贝宠约洗，足不出户享精致洗护～',
      path: `/pages/index/index?aUserId=${this.data?.userInfo?.id || ''}&shareCode=${
        this.data?.userInfo?.promotionCode || ''
      }&shareTime=${Date.now()}`,
      imageUrl: 'https://xian7.zos.ctyun.cn/pet/static/share.png',
      // imageUrl: '/assets/share.png' // 这里填写图片的相对路径或绝对路径
    };
  },
  onShareTimeline: function () {
    return {
      title: '「忙到没空送洗？上门服务解忧！」贝宠约洗，专业到家超省心～',
      imageUrl: 'https://xian7.zos.ctyun.cn/pet/static/share1.png',
      query: `aUserId=${this.data?.userInfo?.id || ''}&shareCode=${
        this.data?.userInfo?.promotionCode || ''
      }&shareTime=${Date.now()}`,
      // imageUrl: '/assets/share.png' // 这里填写图片的相对路径或绝对路径
    };
  },

  /**
   * 购买权益卡或优惠券
   * @param {Object} e 事件对象
   */
  buyItem(e) {
    const { id, type } = e.currentTarget.dataset;
    const userInfo = this.data.userInfo;

    // 检查用户是否登录
    if (!userInfo) {
      wx.navigateTo({
        url: '/pages/login/index',
      });
      return;
    }

    // 保存当前操作的ID和类型
    this.setData({
      currentId: id,
      currentType: type,
    });

    // 根据类型调用不同的购买API
    if (type === 'rightsCard') {
      this.buyRightsCard(id);
    } else if (type === 'coupon') {
      this.buyCoupon(id);
    }
  },

  /**
   * 购买权益卡
   * @param {string} cardId 权益卡ID
   */
  async buyRightsCard(cardId) {
    const _this = this;
    const userInfo = this.data.userInfo;

    try {
      wx.showLoading({
        title: '处理中',
      });

      // 调用购买API
      const order = await rightsCardApi.buy(userInfo.id, cardId, '用户购买权益卡');

      wx.hideLoading();

      if (!order) {
        wx.showToast({
          title: '创建订单失败，请稍后再试！',
          icon: 'none',
        });
        return;
      }

      // 如果是免费的权益卡，直接完成支付
      if (Number(order.amount) === 0) {
        // 调用完成支付API
        await rightsCardApi.pay(order.sn, userInfo.id);
        // 刷新用户信息以确保权益卡信息更新
        await _this.refreshUserInfo();
        // 显示自定义模态框
        _this.setData({
          showModal: true,
          modalTitle: '购买成功',
          modalContent: '权益卡已添加到您的账户！',
        });
        return;
      }

      // 调用支付API
      payApi.doPay({
        sn: order.sn,
        onOk: () => {
          // 调用完成支付API
          rightsCardApi.pay(order.sn, userInfo.id).then(() => {
            // 清理权益卡相关缓存
            _this.clearRightsCardCache();
            // 显示自定义模态框
            _this.setData({
              showModal: true,
              modalTitle: '购买成功',
              modalContent: '权益卡已添加到您的账户！',
            });
          });
        },
        onCancel: () => {
          wx.showToast({
            title: '取消支付',
            icon: 'none',
          });
        },
        onError: () => {
          wx.showToast({
            title: '支付失败',
            icon: 'none',
          });
        },
        complete: () => {},
      });
    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: '购买失败，请稍后再试',
        icon: 'none',
      });
      console.error('购买权益卡失败:', error);
    }
  },

  /**
   * 购买优惠券
   * @param {string} couponId 优惠券ID
   */
  async buyCoupon(couponId) {
    const _this = this;
    const userInfo = this.data.userInfo;

    try {
      wx.showLoading({
        title: '处理中',
      });

      // 调用购买API
      const order = await couponApi.buy(userInfo.id, couponId, '用户购买优惠券');

      wx.hideLoading();

      if (!order) {
        wx.showToast({
          title: '创建订单失败，请稍后再试！',
          icon: 'none',
        });
        return;
      }

      // 如果是免费的优惠券，直接完成支付
      if (Number(order.amount) === 0) {
        // 调用完成支付API
        await couponApi.pay(order.sn, userInfo.id);
        // 清理优惠券相关缓存
        _this.clearCouponCache();
        // 显示自定义模态框
        _this.setData({
          showModal: true,
          modalTitle: '购买成功',
          modalContent: '优惠券已添加到您的账户！',
        });
        return;
      }

      // 调用支付API
      payApi.doPay({
        sn: order.sn,
        onOk: () => {
          // 调用完成支付API
          couponApi.pay(order.sn, userInfo.id).then(() => {
            // 清理优惠券相关缓存
            _this.clearCouponCache();
            // 显示自定义模态框
            _this.setData({
              showModal: true,
              modalTitle: '购买成功',
              modalContent: '优惠券已添加到您的账户！',
            });
          });
        },
        onCancel: () => {
          wx.showToast({
            title: '取消支付',
            icon: 'none',
          });
        },
        onError: () => {
          wx.showToast({
            title: '支付失败',
            icon: 'none',
          });
        },
        complete: () => {},
      });
    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: '购买失败，请稍后再试',
        icon: 'none',
      });
      console.error('购买优惠券失败:', error);
    }
  },

  /**
   * 清理权益卡相关缓存
   */
  clearRightsCardCache() {
    const userInfo = this.data.userInfo;
    if (userInfo && userInfo.id) {
      // 清理权益卡相关的缓存
      requestManager.clearCache('available-membership-cards');
      requestManager.clearCache('valid-membership-cards');
      requestManager.clearCache(`customers/${userInfo.id}/membership-cards`);
      console.log('已清理权益卡相关缓存');
    }
  },

  /**
   * 清理优惠券相关缓存
   */
  clearCouponCache() {
    const userInfo = this.data.userInfo;
    if (userInfo && userInfo.id) {
      // 清理优惠券相关的缓存
      requestManager.clearCache('available-coupons');
      requestManager.clearCache('valid-coupons');
      requestManager.clearCache(`customers/${userInfo.id}/coupons`);
      console.log('已清理优惠券相关缓存');
    }
  },

  /**
   * 模态框确认按钮点击事件
   */
  onModalConfirm() {
    this.setData({
      showModal: false,
      currentId: '',
      currentType: '',
    });
  },
});
