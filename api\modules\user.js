import request, { analysisRes } from "../request";
import config from "../config";
import requestManager from "../../utils/requestManager.js";

const { user } = config.apiUrls;

export default {
  // 登录，若用户已注册则返回openid和user,否则只返回openid，若openid都没返回，则说明接口调用失败了
  async login(params) {
    const res = await request.post(user.login, params);
    const data = analysisRes(res);
    return data;
  },

  // 授权获取用户手机号
  async getPhoneNumber(code) {
    const res = await request.get(user.getPhoneNumber, {
      code,
      loadingText: "登录中...",
    });
    const data = analysisRes(res);
    return data;
  },

  // 用户注册
  async register(info) {
    console.log("register: ", info);
    const res = await request.post(user.register, info);
    const data = analysisRes(res);
    console.log(data);
    return data;
  },

  // 获取用户信息
  async getProfile(id) {
    const res = await request.get(user.getProfile.replace("{id}", id));
    return analysisRes(res);
  },

  // 更新用户信息
  async updateProfile(data) {
    const res = await request.put(user.updateProfile, data);
    return analysisRes(res);
  },

  // 上传头像
  uploadAvatar(filePath) {
    return request.upload(user.uploadAvatar, filePath);
  },

  // 查询宠物列表
  async getPets(id) {
    const url = user.getPets.replace("{id}", id);
    return requestManager.request(
      url,
      { id },
      async () => {
        const res = await request.get(url);
        return analysisRes(res);
      },
      { cacheTime: 10000 } // 缓存10秒
    );
  },

  // addPet: "/customers/{id}/pet", // 新增宠物
  async addPet(id, data) {
    const res = await request.post(user.addPet.replace("{id}", id), data);
    return analysisRes(res);
  },

  // editPet: "/customers/{id}/pet/{petId}", // 编辑宠物
  async editPet(id, petId, data) {
    const res = await request.put(
      user.editPet.replace("{id}", id).replace("{petId}", petId),
      data
    );
    return analysisRes(res);
  },

  // delPet: "/customers/{id}/pet/{petId}", // 删除宠物
  async delPet(id, petId) {
    const res = await request.delete(
      user.delPet.replace("{id}", id).replace("{petId}", petId)
    );
    return analysisRes(res);
  },

  // getLastServiceTime: "/customers/{id}/pets/{petId}/last-service-time", // 查询指定宠物最后一次洗护完成时间
  async getLastServiceTime(id, petId) {
    const url = user.getLastServiceTime.replace("{id}", id).replace("{petId}", petId);
    return requestManager.request(
      url,
      { id, petId },
      async () => {
        const res = await request.get(url);
        return analysisRes(res);
      },
      { cacheTime: 30000 } // 缓存30秒，因为最后服务时间变化较少
    );
  },
};
