// pages/complaint/detail.js
import complaintApi from '../../api/modules/complaint';
import Session from '../../common/Session';
import TimeUtils from '../../common/TimeUtils.js';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    userInfo: null,
    complaintDetail: null,
    loading: true,
    
    // 状态映射
    statusMap: {
      pending: { text: '待处理', color: '#ff9500', desc: '您的投诉建议已提交，我们会尽快处理' },
      processing: { text: '处理中', color: '#007aff', desc: '我们正在处理您的投诉建议，请耐心等待' },
      resolved: { text: '已解决', color: '#34c759', desc: '您的投诉建议已处理完成' },
      closed: { text: '已关闭', color: '#8e8e93', desc: '投诉建议已关闭' }
    },
    
    // 分类映射
    categoryMap: {
      complaint: '投诉',
      suggestion: '建议'
    },
    
    subCategoryMap: {
      order: '订单投诉',
      employee: '人员投诉',
      platform: '平台建议',
      service: '服务建议'
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const userInfo = Session.getUser();
    this.setData({ userInfo });
    
    if (options.id) {
      this.loadComplaintDetail(options.id);
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  /**
   * 加载投诉建议详情
   */
  async loadComplaintDetail(complaintId) {
    try {
      const { userInfo } = this.data;
      if (!userInfo) {
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        });
        return;
      }

      const detail = await complaintApi.detail(userInfo.id, complaintId);
      if (detail) {
        this.setData({ complaintDetail: detail });
      } else {
        wx.showToast({
          title: '加载失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('加载投诉建议详情失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 预览图片
   */
  previewImage(e) {
    const url = e.currentTarget.dataset.url;
    const { complaintDetail } = this.data;
    wx.previewImage({
      current: url,
      urls: complaintDetail.photoURLs || []
    });
  },

  /**
   * 编辑投诉建议
   */
  editComplaint() {
    const { complaintDetail } = this.data;

    if (complaintDetail.status !== 'pending') {
      wx.showToast({
        title: '只有待处理状态的投诉建议才能编辑',
        icon: 'none'
      });
      return;
    }

    // 跳转到创建页面，传递编辑参数
    wx.navigateTo({
      url: `/pages/complaint/index?mode=edit&id=${complaintDetail.id}`
    });
  },

  /**
   * 删除投诉建议
   */
  deleteComplaint() {
    const { complaintDetail } = this.data;
    
    if (complaintDetail.status !== 'pending') {
      wx.showToast({
        title: '只有待处理状态的投诉建议才能删除',
        icon: 'none'
      });
      return;
    }
    
    wx.showModal({
      title: '确认删除',
      content: '删除后无法恢复，确定要删除这条投诉建议吗？',
      success: async (res) => {
        if (res.confirm) {
          await this.performDelete();
        }
      }
    });
  },

  /**
   * 执行删除
   */
  async performDelete() {
    try {
      wx.showLoading({ title: '删除中...' });
      
      const { userInfo, complaintDetail } = this.data;
      await complaintApi.delete(userInfo.id, complaintDetail.id);
      
      wx.showToast({
        title: '删除成功',
        icon: 'success'
      });
      
      // 返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    } catch (error) {
      console.error('删除投诉建议失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 联系客服
   */
  contactService() {
    // 这里可以跳转到客服页面或拨打客服电话
    wx.showModal({
      title: '联系客服',
      content: '如需进一步沟通，请联系客服：18591969898',
      confirmText: '拨打电话',
      success: (res) => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: '18591969898'
          });
        }
      }
    });
  },

  /**
   * 格式化时间
   */
  formatTime(timestamp) {
    return TimeUtils.formatTime(timestamp);
  }
});
