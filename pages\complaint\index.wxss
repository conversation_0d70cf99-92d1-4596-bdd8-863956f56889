/* pages/complaint/index.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 页面标题 */
.header {
  background-color: #fff;
  padding: 40rpx 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid #eee;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.header-actions {
  display: flex;
  align-items: center;
}

.history-btn {
  font-size: 26rpx;
  color: #ff4f8f;
  padding: 12rpx 24rpx;
  border: 1rpx solid #ff4f8f;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.history-btn:active {
  background-color: #ff4f8f;
  color: #fff;
}

/* 通用区块样式 */
.category-section,
.subcategory-section,
.order-section,
.employee-section,
.title-section,
.content-section,
.contact-section,
.photo-section {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
}

.required {
  color: #ff4757;
  margin-left: 8rpx;
}

.optional-text {
  font-size: 24rpx;
  color: #999;
  margin-left: 16rpx;
  font-weight: normal;
}

/* 分类选择 */
.category-container {
  display: flex;
  gap: 20rpx;
}

.category-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 16rpx;
  background-color: #fafafa;
  transition: all 0.3s ease;
}

.category-item.active {
  border-color: #ff4f8f;
  background-color: #fff5f8;
}

.category-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 16rpx;
}

.category-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 子分类选择 */
.subcategory-container {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.subcategory-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 12rpx;
  background-color: #fafafa;
  transition: all 0.3s ease;
}

.subcategory-item.active {
  border-color: #ff4f8f;
  background-color: #fff5f8;
}

.subcategory-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.subcategory-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.subcategory-desc {
  font-size: 24rpx;
  color: #666;
}

.subcategory-check {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: #ff4f8f;
  display: flex;
  align-items: center;
  justify-content: center;
}

.check-icon {
  color: #fff;
  font-size: 24rpx;
  font-weight: bold;
}

/* 订单相关 */
.selected-order {
  border: 2rpx solid #ff4f8f;
  border-radius: 12rpx;
  padding: 20rpx;
  background-color: #fff5f8;
}

.order-content {
  display: flex;
  align-items: center;
}

.service-image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.order-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.service-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.order-sn {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.order-price {
  font-size: 26rpx;
  color: #ff4f8f;
  font-weight: 600;
}

.order-actions {
  display: flex;
  align-items: center;
}

.clear-btn {
  font-size: 24rpx;
  color: #999;
  padding: 8rpx 16rpx;
  border: 1rpx solid #ddd;
  border-radius: 20rpx;
}

.select-order-btn,
.select-employee-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border: 2rpx dashed #ddd;
  border-radius: 12rpx;
  background-color: #fafafa;
}

.select-text {
  font-size: 28rpx;
  color: #666;
}

.arrow {
  font-size: 24rpx;
  color: #999;
}

/* 员工相关 */
.selected-employee {
  border: 2rpx solid #ff4f8f;
  border-radius: 12rpx;
  padding: 20rpx;
  background-color: #fff5f8;
}

.employee-content {
  display: flex;
  align-items: center;
}

.employee-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.employee-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.employee-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.employee-phone {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.employee-position {
  font-size: 24rpx;
  color: #ff4f8f;
  font-weight: 500;
}

.employee-actions {
  display: flex;
  align-items: center;
}

/* 输入框样式 */
.title-input,
.contact-input {
  width: 100%;
  min-height: 80rpx;
  padding: 20rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 12rpx;
  font-size: 28rpx;
  background-color: #fafafa;
}

.title-input:focus,
.contact-input:focus {
  border-color: #ff4f8f;
  background-color: #fff;
}

.contact-input.error {
  border-color: #ff4757;
  background-color: #fff5f5;
}

.content-input {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 12rpx;
  font-size: 28rpx;
  line-height: 1.6;
  background-color: #fafafa;
}

.content-input:focus {
  border-color: #ff4f8f;
  background-color: #fff;
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 12rpx;
}

/* 错误提示 */
.error-tip {
  margin-top: 12rpx;
}

.error-text {
  font-size: 24rpx;
  color: #ff4757;
  line-height: 1.4;
}

/* 图片上传 */
.photo-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.photo-item {
  position: relative;
  width: 160rpx;
  height: 160rpx;
}

.photo-preview {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  border: 2rpx solid #f0f0f0;
}

.photo-delete {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 32rpx;
  height: 32rpx;
  background-color: #ff4757;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.3);
}

.delete-icon {
  color: #fff;
  font-size: 20rpx;
  font-weight: bold;
}

.photo-upload {
  width: 160rpx;
  height: 160rpx;
  border: 2rpx dashed #ddd;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
  transition: all 0.3s ease;
}

.photo-upload:active {
  background-color: #f0f0f0;
  border-color: #ff4f8f;
}

.upload-image {
  width: 100%;
  height: 100%;
}

.photo-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 16rpx;
  text-align: center;
}

/* 提交按钮 */
.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 30rpx;
  border-top: 1rpx solid #eee;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.05);
  z-index: 10;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
  background-color: #ddd;
  color: #999;
  border: none;
  transition: all 0.3s ease;
}

.submit-btn.active {
  background: linear-gradient(135deg, #ff4f8f 0%, #ff7ba7 100%);
  color: #fff;
  box-shadow: 0 6rpx 20rpx rgba(255, 79, 143, 0.3);
}

.submit-btn.active:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 16rpx rgba(255, 79, 143, 0.3);
}

/* 订单选择弹窗 */
.order-modal,
.employee-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;
}

.modal-content {
  width: 100%;
  max-height: 80vh;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.order-list,
.employee-list {
  flex: 1;
  padding: 0 30rpx 30rpx;
}

.order-item,
.employee-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.order-item:last-child,
.employee-item:last-child {
  border-bottom: none;
}

.select-arrow {
  font-size: 24rpx;
  color: #999;
  margin-left: 20rpx;
}

/* 员工搜索 */
.search-section {
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.search-input {
  flex: 1;
  height: 70rpx;
  padding: 0 20rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 35rpx;
  font-size: 26rpx;
  background-color: #fafafa;
}

.search-input:focus {
  border-color: #ff4f8f;
  background-color: #fff;
}

.search-btn {
  padding: 16rpx 32rpx;
  background-color: #ff4f8f;
  color: #fff;
  border-radius: 35rpx;
  font-size: 26rpx;
}

/* 员工列表项 */
.employee-item .employee-info {
  margin-left: 20rpx;
}

.employee-item .employee-name {
  margin-bottom: 6rpx;
}

.employee-item .employee-phone {
  margin-bottom: 4rpx;
}

.employee-item .employee-position {
  margin-bottom: 4rpx;
}

.employee-exp {
  margin-top: 4rpx;
}

.exp-text {
  font-size: 22rpx;
  color: #999;
}

/* 加载状态 */
.loading-more {
  text-align: center;
  padding: 30rpx;
  color: #999;
  font-size: 26rpx;
}

.empty-tip {
  text-align: center;
  padding: 80rpx 0;
  color: #999;
  font-size: 28rpx;
}

/* 加载遮罩 */
.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background-color: #fff;
  padding: 60rpx 80rpx;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #ff4f8f;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}
