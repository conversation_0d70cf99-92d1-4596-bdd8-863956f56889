/**
 * 请求管理器 - 防止重复请求
 * 通过缓存正在进行的请求，避免短时间内重复发起相同的API调用
 */

class RequestManager {
  constructor() {
    // 存储正在进行的请求
    this.pendingRequests = new Map();
    // 请求结果缓存
    this.cache = new Map();
    // 默认缓存时间（毫秒）
    this.defaultCacheTime = 5000; // 5秒
  }

  /**
   * 生成请求的唯一键
   * @param {string} url 请求URL
   * @param {object} params 请求参数
   * @returns {string} 唯一键
   */
  generateKey(url, params = {}) {
    const sortedParams = Object.keys(params)
      .sort()
      .reduce((result, key) => {
        result[key] = params[key];
        return result;
      }, {});
    
    return `${url}?${JSON.stringify(sortedParams)}`;
  }

  /**
   * 执行请求（带防重复和缓存机制）
   * @param {string} url 请求URL
   * @param {object} params 请求参数
   * @param {function} requestFn 实际的请求函数
   * @param {object} options 选项
   * @param {number} options.cacheTime 缓存时间（毫秒），0表示不缓存
   * @param {boolean} options.forceRefresh 是否强制刷新
   * @returns {Promise} 请求结果
   */
  async request(url, params = {}, requestFn, options = {}) {
    const {
      cacheTime = this.defaultCacheTime,
      forceRefresh = false
    } = options;

    const key = this.generateKey(url, params);
    
    console.log(`[RequestManager] 请求: ${key}`);

    // 检查缓存
    if (!forceRefresh && cacheTime > 0) {
      const cached = this.cache.get(key);
      if (cached && Date.now() - cached.timestamp < cacheTime) {
        console.log(`[RequestManager] 使用缓存: ${key}`);
        return cached.data;
      }
    }

    // 检查是否有正在进行的相同请求
    if (this.pendingRequests.has(key)) {
      console.log(`[RequestManager] 等待进行中的请求: ${key}`);
      return this.pendingRequests.get(key);
    }

    // 创建新请求
    const requestPromise = this.executeRequest(key, requestFn, cacheTime);
    this.pendingRequests.set(key, requestPromise);

    return requestPromise;
  }

  /**
   * 执行实际请求
   * @param {string} key 请求键
   * @param {function} requestFn 请求函数
   * @param {number} cacheTime 缓存时间
   * @returns {Promise} 请求结果
   */
  async executeRequest(key, requestFn, cacheTime) {
    try {
      console.log(`[RequestManager] 执行请求: ${key}`);
      const result = await requestFn();
      
      // 缓存结果
      if (cacheTime > 0) {
        this.cache.set(key, {
          data: result,
          timestamp: Date.now()
        });
      }

      console.log(`[RequestManager] 请求完成: ${key}`);
      return result;
    } catch (error) {
      console.error(`[RequestManager] 请求失败: ${key}`, error);
      throw error;
    } finally {
      // 清除正在进行的请求记录
      this.pendingRequests.delete(key);
    }
  }

  /**
   * 清除缓存
   * @param {string} pattern 匹配模式（可选）
   */
  clearCache(pattern) {
    if (pattern) {
      // 清除匹配的缓存
      for (const [key] of this.cache) {
        if (key.includes(pattern)) {
          this.cache.delete(key);
        }
      }
    } else {
      // 清除所有缓存
      this.cache.clear();
    }
  }

  /**
   * 清除过期缓存
   */
  clearExpiredCache() {
    const now = Date.now();
    for (const [key, value] of this.cache) {
      if (now - value.timestamp > this.defaultCacheTime) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * 取消正在进行的请求
   * @param {string} pattern 匹配模式（可选）
   */
  cancelPendingRequests(pattern) {
    if (pattern) {
      for (const [key] of this.pendingRequests) {
        if (key.includes(pattern)) {
          this.pendingRequests.delete(key);
        }
      }
    } else {
      this.pendingRequests.clear();
    }
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      pendingCount: this.pendingRequests.size,
      cacheCount: this.cache.size,
      pendingKeys: Array.from(this.pendingRequests.keys()),
      cacheKeys: Array.from(this.cache.keys())
    };
  }
}

// 创建全局实例
const requestManager = new RequestManager();

// 定期清理过期缓存
setInterval(() => {
  requestManager.clearExpiredCache();
}, 60000); // 每分钟清理一次

export default requestManager;
