/**
 * 统一的时间处理工具类
 * 解决项目中时间格式不一致的问题
 */

class TimeUtils {
  /**
   * 安全的日期解析函数
   * @param {Date|string|number} input - 输入的日期
   * @returns {Date|null} 解析后的日期对象或null
   */
  static parseDate(input) {
    if (!input) return null;

    let date;
    if (typeof input === 'string') {
      // 处理常见的日期字符串格式
      if (input.includes('T') || input.includes('Z')) {
        // ISO 格式
        date = new Date(input);
      } else if (input.match(/^\d{4}-\d{2}-\d{2}( \d{2}:\d{2}(:\d{2})?)?$/)) {
        // YYYY-MM-DD 或 YYYY-MM-DD HH:mm:ss 格式
        date = new Date(input);
      } else {
        // 其他格式尝试直接解析
        date = new Date(input);
      }
    } else if (typeof input === 'number') {
      // 时间戳处理（自动判断秒或毫秒）
      date = new Date(input < 10000000000 ? input * 1000 : input);
    } else if (input instanceof Date) {
      date = input;
    } else {
      return null;
    }

    return isNaN(date.getTime()) ? null : date;
  }

  /**
   * 验证日期格式是否正确
   * @param {any} input - 输入的日期
   * @returns {boolean} 是否为有效日期
   */
  static isValidDate(input) {
    const date = this.parseDate(input);
    return date !== null;
  }

  /**
   * 标准时间格式化 (YYYY-MM-DD HH:mm)
   * @param {Date|string|number} input - 输入的日期
   * @returns {string} 格式化后的时间字符串
   */
  static formatTime(input) {
    if (!input) return '';

    const date = this.parseDate(input);
    if (!date) {
      console.error('Invalid date format:', input);
      return '';
    }

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}`;
  }

  /**
   * 完整时间格式化 (YYYY-MM-DD HH:mm:ss)
   * @param {Date|string|number} input - 输入的日期
   * @returns {string} 格式化后的时间字符串
   */
  static formatDateTime(input) {
    if (!input) return '';

    const date = this.parseDate(input);
    if (!date) {
      console.error('Invalid date format:', input);
      return '';
    }

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }

  /**
   * 日期格式化 (YYYY-MM-DD)
   * @param {Date|string|number} input - 输入的日期
   * @returns {string} 格式化后的日期字符串
   */
  static formatDate(input) {
    if (!input) return '';

    const date = this.parseDate(input);
    if (!date) {
      console.error('Invalid date format:', input);
      return '';
    }

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  }

  /**
   * 相对时间格式化（几分钟前、几小时前等）
   * @param {Date|string|number} input - 输入的日期
   * @returns {string} 相对时间字符串
   */
  static formatRelativeTime(input) {
    if (!input) return '';

    const date = this.parseDate(input);
    if (!date) return '';

    const now = new Date();
    const diff = now - date;

    if (diff < 60000) { // 1分钟内
      return '刚刚';
    } else if (diff < 3600000) { // 1小时内
      return Math.floor(diff / 60000) + '分钟前';
    } else if (diff < 86400000) { // 1天内
      return Math.floor(diff / 3600000) + '小时前';
    } else if (diff < 604800000) { // 1周内
      return Math.floor(diff / 86400000) + '天前';
    } else {
      return this.formatDate(date);
    }
  }

  /**
   * 本地化时间格式化
   * @param {Date|string|number} input - 输入的日期
   * @param {object} options - 格式化选项
   * @returns {string} 本地化格式的时间字符串
   */
  static formatLocaleTime(input, options = {}) {
    if (!input) return '';

    const date = this.parseDate(input);
    if (!date) return '';

    const defaultOptions = {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    };

    return date.toLocaleString('zh-CN', { ...defaultOptions, ...options });
  }

  /**
   * 获取当前时间戳（毫秒）
   * @returns {number} 当前时间戳
   */
  static now() {
    return Date.now();
  }

  /**
   * 获取当前日期时间字符串
   * @returns {string} 当前日期时间
   */
  static nowString() {
    return this.formatDateTime(new Date());
  }

  /**
   * 判断是否为今天
   * @param {Date|string|number} input - 输入的日期
   * @returns {boolean} 是否为今天
   */
  static isToday(input) {
    const date = this.parseDate(input);
    if (!date) return false;

    const today = new Date();
    return date.toDateString() === today.toDateString();
  }

  /**
   * 判断是否为未来时间
   * @param {Date|string|number} input - 输入的日期
   * @returns {boolean} 是否为未来时间
   */
  static isFuture(input) {
    const date = this.parseDate(input);
    if (!date) return false;

    return date > new Date();
  }

  /**
   * 计算两个日期之间的天数差
   * @param {Date|string|number} date1 - 日期1
   * @param {Date|string|number} date2 - 日期2
   * @returns {number} 天数差（date1 - date2）
   */
  static daysDiff(date1, date2) {
    const d1 = this.parseDate(date1);
    const d2 = this.parseDate(date2);
    
    if (!d1 || !d2) return 0;

    const diffTime = d1 - d2;
    return Math.floor(diffTime / (1000 * 60 * 60 * 24));
  }
}

export default TimeUtils;
