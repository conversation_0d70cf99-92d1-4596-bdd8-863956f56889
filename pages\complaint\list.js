// pages/complaint/list.js
import complaintApi from '../../api/modules/complaint';
import Session from '../../common/Session';
import TimeUtils from '../../common/TimeUtils.js';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    userInfo: null,
    complaintList: [],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 10,
    
    // 筛选条件
    currentTab: 'all',
    tabs: [
      { key: 'all', label: '全部' },
      { key: 'complaint', label: '投诉' },
      { key: 'suggestion', label: '建议' }
    ],
    
    // 状态映射
    statusMap: {
      pending: { text: '待处理', color: '#ff9500' },
      processing: { text: '处理中', color: '#007aff' },
      resolved: { text: '已解决', color: '#34c759' },
      closed: { text: '已关闭', color: '#8e8e93' }
    },
    
    // 分类映射
    categoryMap: {
      complaint: '投诉',
      suggestion: '建议'
    },
    
    subCategoryMap: {
      order: '订单投诉',
      employee: '人员投诉',
      platform: '平台建议',
      service: '服务建议'
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const userInfo = Session.getUser();
    this.setData({ userInfo });
    this.loadComplaintList();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 刷新列表
    this.refreshList();
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.refreshList();
  },

  /**
   * 上拉加载更多
   */
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMore();
    }
  },

  /**
   * 切换标签
   */
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    if (tab === this.data.currentTab) return;
    
    this.setData({ currentTab: tab });
    this.refreshList();
  },

  /**
   * 刷新列表
   */
  refreshList() {
    this.setData({
      complaintList: [],
      page: 1,
      hasMore: true
    });
    this.loadComplaintList();
  },

  /**
   * 加载更多
   */
  loadMore() {
    this.setData({
      page: this.data.page + 1
    });
    this.loadComplaintList();
  },

  /**
   * 加载投诉建议列表
   */
  async loadComplaintList() {
    const { userInfo, page, pageSize, currentTab, complaintList } = this.data;
    
    if (!userInfo) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    this.setData({ loading: true });

    try {
      const params = {
        page,
        pageSize
      };
      
      // 添加分类筛选
      if (currentTab !== 'all') {
        params.category = currentTab;
      }

      const result = await complaintApi.list(userInfo.id, params);
      
      if (result && result.list) {
        const newList = page === 1 ? result.list : [...complaintList, ...result.list];
        this.setData({
          complaintList: newList,
          hasMore: result.list.length === pageSize
        });
      } else {
        this.setData({
          complaintList: page === 1 ? [] : complaintList,
          hasMore: false
        });
      }
    } catch (error) {
      console.error('加载投诉建议列表失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
      wx.stopPullDownRefresh();
    }
  },

  /**
   * 查看详情
   */
  viewDetail(e) {
    const complaint = e.currentTarget.dataset.complaint;
    wx.navigateTo({
      url: `/pages/complaint/detail?id=${complaint.id}`
    });
  },

  /**
   * 编辑投诉建议
   */
  editComplaint(e) {
    const complaint = e.currentTarget.dataset.complaint;

    // 只有待处理状态的投诉建议才能编辑
    if (complaint.status !== 'pending') {
      wx.showToast({
        title: '只有待处理状态的投诉建议才能编辑',
        icon: 'none'
      });
      return;
    }

    // 跳转到创建页面，传递编辑参数
    wx.navigateTo({
      url: `/pages/complaint/index?mode=edit&id=${complaint.id}`
    });
  },

  /**
   * 删除投诉建议
   */
  deleteComplaint(e) {
    const complaint = e.currentTarget.dataset.complaint;
    
    // 只有待处理状态的投诉建议才能删除
    if (complaint.status !== 'pending') {
      wx.showToast({
        title: '只有待处理状态的投诉建议才能删除',
        icon: 'none'
      });
      return;
    }
    
    wx.showModal({
      title: '确认删除',
      content: '删除后无法恢复，确定要删除这条投诉建议吗？',
      success: async (res) => {
        if (res.confirm) {
          await this.performDelete(complaint.id);
        }
      }
    });
  },

  /**
   * 执行删除
   */
  async performDelete(complaintId) {
    try {
      wx.showLoading({ title: '删除中...' });
      
      const { userInfo } = this.data;
      await complaintApi.delete(userInfo.id, complaintId);
      
      wx.showToast({
        title: '删除成功',
        icon: 'success'
      });
      
      // 刷新列表
      this.refreshList();
    } catch (error) {
      console.error('删除投诉建议失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 创建新的投诉建议
   */
  createComplaint() {
    wx.navigateTo({
      url: '/pages/complaint/index'
    });
  },

  /**
   * 格式化时间
   */
  formatTime(timestamp) {
    return TimeUtils.formatRelativeTime(timestamp);
  }
});
