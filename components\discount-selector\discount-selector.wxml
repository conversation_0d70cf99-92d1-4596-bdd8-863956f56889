<!-- components/discount-selector/discount-selector.wxml -->
<view class="discount-selector-modal {{show ? 'show' : ''}}" catchtouchmove="preventTouchMove" style="display: {{show ? 'flex' : 'none'}}">
  <view class="discount-selector-mask" catchtap="close"></view>
  <view class="discount-selector-container">
    <view class="discount-selector-header">
      <text class="discount-selector-title">选择优惠</text>
      <view class="discount-selector-close" catchtap="close">×</view>
    </view>

    <view class="discount-selector-tabs">
      <view wx:if="{{timesCards.length > 0}}" class="discount-tab {{currentTab === 'times' ? 'active' : ''}}" bindtap="switchTab" data-tab="times">次卡</view>
      <view wx:if="{{availableCoupons.length > 0}}" class="discount-tab {{currentTab === 'coupon' ? 'active' : ''}}" bindtap="switchTab" data-tab="coupon">优惠券</view>
    </view>

    <view class="discount-selector-content">
      <!-- 次卡列表 -->
      <view wx:if="{{currentTab === 'times'}}" class="discount-list">
        <view wx:for="{{timesCards}}" wx:key="id" class="discount-item {{selectedTimesCardId === item.id ? 'selected' : ''}}" bindtap="selectTimesCard" data-id="{{item.id}}">
          <view class="discount-item-left">
            <view class="discount-item-name">{{item.name}}</view>
            <view class="discount-item-desc">剩余次数: {{item.remainTimes === -1 ? '不限' : item.remainTimes + '次'}}</view>
            <view wx:if="{{item.expireDate}}" class="discount-item-date">有效期至: {{item.expireDate}}</view>
            <view class="discount-item-tip">此卡可抵扣本次服务全部费用</view>
          </view>
          <view class="discount-item-right">
            <view class="discount-item-value">免费</view>
            <view class="discount-item-select">
              <text wx:if="{{selectedTimesCardId === item.id}}">✓</text>
            </view>
          </view>
        </view>
        <view wx:if="{{timesCards.length === 0}}" class="discount-empty">暂无可用次卡</view>
      </view>

      <!-- 优惠券列表 -->
      <view wx:if="{{currentTab === 'coupon'}}" class="discount-list">
        <view wx:for="{{availableCoupons}}" wx:key="id" class="discount-item {{selectedCouponId === item.id ? 'selected' : ''}}" bindtap="selectCoupon" data-id="{{item.id}}">
          <view class="discount-item-left">
            <view class="discount-item-name">{{item.coupon.description || ''}}</view>
            <view class="discount-item-desc">
              <text wx:if="{{item.threshold}}">满{{item.threshold}}元减{{item.amount}}元</text>
              <text wx:else>直减{{item.amount}}元</text>
            </view>
            <view wx:if="{{item.expireDate}}" class="discount-item-date">有效期至: {{item.expireDate}}</view>
            <view class="discount-item-desc">剩余次数: {{item.remainTimes === -1 ? '不限' : item.remainTimes + '次'}}</view>
            <view class="discount-item-tip">
              <text wx:if="{{discountedPrice < item.amount}}">最高可抵扣{{discountedPrice}}元</text>
              <text wx:else>最高可抵扣{{item.amount}}元</text>
            </view>
          </view>
          <view class="discount-item-right">
            <view class="discount-item-value">￥{{item.amount}}</view>
            <view class="discount-item-select">
              <text wx:if="{{selectedCouponId === item.id}}">✓</text>
            </view>
          </view>
        </view>
        <view wx:if="{{availableCoupons.length === 0}}" class="discount-empty">暂无可用优惠券</view>
      </view>
    </view>

    <view class="discount-selector-footer">
      <view class="discount-selector-total">
        <text class="discount-selector-total-label">合计：</text>
        <text class="discount-selector-total-value">￥{{previewPrice}}</text>
      </view>
      <view class="discount-selector-btn" catchtap="confirmDiscount">确定</view>
    </view>
  </view>
</view>
