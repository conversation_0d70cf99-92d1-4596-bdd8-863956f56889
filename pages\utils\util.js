// 引入统一的时间处理工具
import TimeUtils from '../../common/TimeUtils.js';

/**
 * 消息通知时间的格式化
 * @param {*} timeStamp
 */
function formatDate(timeStamp) {
  const date = new Date(timeStamp);
  const now = new Date();
  const diffInSeconds = Math.floor((now - date) / 1000);
  const diffInDays = Math.floor(diffInSeconds / (60 * 60 * 24));

  if (diffInDays === 0) {
    return `${padZero(date.getHours())}:${padZero(date.getMinutes())}`;
  } else if (diffInDays <= 3) {
    return `${date.getMonth() + 1}月${date.getDate()}日 ${padZero(date.getHours())}:${padZero(date.getMinutes())}`;
  } else {
    return `${date.getMonth() + 1}月${date.getDate()}日`;
  }
}
/**
 * 数据库返回时间的日常格式化
 * @param {*} date
 */
function formatNormalDate(date) {
  if (!date) return '';
  if (typeof date === 'string') {
    date = new Date(date);
  } else if (typeof date === 'number') {
    date = new Date(date * 1000); // 如果是时间戳，转换为毫秒
  } else if (!(date instanceof Date)) {
    console.error('Invalid date format:', date);
    return '';
  }
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

function removeValue(array, valueToRemove) {
  let index = array.indexOf(valueToRemove);
  while (index !== -1) {
    array.splice(index, 1); // 删除指定索引的元素
    index = array.indexOf(valueToRemove); // 查找下一个指定值的索引
  }
  return array; // 返回修改后的原数组
}

function padZero(num) {
  return num < 10 ? '0' + num : num;
}

function formatAge(months) {
  if (typeof months !== 'number' || isNaN(months)) {
    console.error('Invalid months value:', months);
    return '无效年龄';
  }
  const years = Math.floor(months / 12);
  const remainingMonths = months % 12;
  if (years > 0) {
    if (remainingMonths > 0) {
      return `${years}岁${remainingMonths}个月`;
    }
    return `${years}岁`;
  }
  if (remainingMonths > 0) {
    return `${remainingMonths}个月`;
  }
  return '-';
}

function formatDateTime(date, format = 'YYYY-MM-DD') {
  if (!date) return '';

  const d = new Date(date);
  if (isNaN(d.getTime())) return '';

  const pad = n => (n < 10 ? `0${n}` : n);

  const replacements = {
    YYYY: d.getFullYear(),
    MM: pad(d.getMonth() + 1),
    DD: pad(d.getDate()),
    HH: pad(d.getHours()),
    mm: pad(d.getMinutes()),
    ss: pad(d.getSeconds()),
  };

  return format.replace(/YYYY|MM|DD|HH|mm|ss/g, match => replacements[match]);
}

function generateId() {
  const timestamp = Date.now().toString(36); // 时间戳转36进制
  const random = Math.random().toString(36).substring(2, 7); // 随机数
  return `${timestamp}-${random}`;
}

/**
 * 格式化最后洗护时间
 * @param {string|null} lastServiceTime - 最后洗护时间，ISO 8601格式
 * @returns {string} 格式化后的时间文本
 */
function formatLastServiceTime(lastServiceTime) {
  if (!lastServiceTime) {
    return '暂无洗护记录';
  }

  const serviceDate = new Date(lastServiceTime);
  const now = new Date();
  const diffInMs = now - serviceDate;
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

  if (diffInDays === 0) {
    return '今天洗护';
  } else if (diffInDays === 1) {
    return '昨天洗护';
  } else if (diffInDays <= 7) {
    return `${diffInDays}天前洗护`;
  } else if (diffInDays <= 30) {
    const weeks = Math.floor(diffInDays / 7);
    return `${weeks}周前洗护`;
  } else if (diffInDays <= 365) {
    const months = Math.floor(diffInDays / 30);
    return `${months}个月前洗护`;
  } else {
    const years = Math.floor(diffInDays / 365);
    return `${years}年前洗护`;
  }
}

/**
 * 通用时间格式化函数
 * @param {Date|string|number} date - 日期对象、日期字符串或时间戳
 * @returns {string} 格式化后的时间字符串
 */
function formatTime(date) {
  return TimeUtils.formatTime(date);
}

/**
 * 安全的日期解析函数
 * @param {Date|string|number} input - 输入的日期
 * @returns {Date|null} 解析后的日期对象或null
 */
function parseDate(input) {
  return TimeUtils.parseDate(input);
}

/**
 * 验证日期格式是否正确
 * @param {any} input - 输入的日期
 * @returns {boolean} 是否为有效日期
 */
function isValidDate(input) {
  return TimeUtils.isValidDate(input);
}

module.exports = {
  formatDateTime,
  formatNormalDate,
  padZero,
  formatAge,
  removeValue,
  formatDate,
  generateId,
  formatLastServiceTime,
  formatTime,
  parseDate,
  isValidDate,
};
