# 时间格式化统一处理方案

## 问题背景

项目中存在时间格式显示不一致的问题，主要表现为：
- 不同页面使用不同的时间格式化方法
- 日期解析时缺乏统一的错误处理
- 时间戳格式（秒/毫秒）处理不统一
- 缺乏统一的日期验证机制

## 解决方案

### 1. 统一时间处理工具类

创建了 `common/TimeUtils.js` 作为统一的时间处理工具类，提供以下功能：

#### 核心方法

- `parseDate(input)` - 安全的日期解析
- `isValidDate(input)` - 日期格式验证
- `formatTime(input)` - 标准时间格式化 (YYYY-MM-DD HH:mm)
- `formatDateTime(input)` - 完整时间格式化 (YYYY-MM-DD HH:mm:ss)
- `formatDate(input)` - 日期格式化 (YYYY-MM-DD)
- `formatRelativeTime(input)` - 相对时间格式化（几分钟前等）
- `formatLocaleTime(input, options)` - 本地化时间格式化

#### 辅助方法

- `now()` - 获取当前时间戳
- `nowString()` - 获取当前时间字符串
- `isToday(input)` - 判断是否为今天
- `isFuture(input)` - 判断是否为未来时间
- `daysDiff(date1, date2)` - 计算日期差

### 2. 支持的输入格式

TimeUtils 支持以下输入格式：

```javascript
// 字符串格式
'2025-07-11 09:00:00'
'2025-07-11T09:00:00Z'
'2025-07-11T09:00:00.000Z'

// 时间戳（自动识别秒/毫秒）
1720684800000  // 毫秒
1720684800     // 秒

// Date 对象
new Date('2025-07-11 09:00:00')
```

### 3. 错误处理

- 所有方法都包含完善的错误处理
- 无效输入返回空字符串或 null
- 控制台输出详细的错误信息便于调试

## 使用方法

### 在 JS 文件中使用

```javascript
import TimeUtils from '../../common/TimeUtils.js';

// 格式化时间
const formattedTime = TimeUtils.formatTime('2025-07-11 09:00:00');
// 输出: "2025-07-11 09:00"

// 验证日期
const isValid = TimeUtils.isValidDate(userInput);
if (!isValid) {
  wx.showToast({ title: '日期格式错误', icon: 'none' });
  return;
}

// 相对时间
const relativeTime = TimeUtils.formatRelativeTime(timestamp);
// 输出: "2小时前" 或 "3天前"
```

### 在 WXML 中使用（WXS）

```xml
<wxs module="timeUtils">
function formatTime(timestamp) {
  if (!timestamp) return '';
  
  var date;
  if (typeof timestamp === 'string') {
    date = getDate(timestamp);
  } else if (typeof timestamp === 'number') {
    date = getDate(timestamp < 10000000000 ? timestamp * 1000 : timestamp);
  } else {
    return '';
  }
  
  if (!date || isNaN(date.getTime())) {
    return '';
  }
  
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
}

module.exports.formatTime = formatTime;
</wxs>

<!-- 使用 -->
<text>{{timeUtils.formatTime(item.createdAt)}}</text>
```

## 已更新的文件

### 核心文件
- `common/TimeUtils.js` - 新增统一时间处理工具类
- `pages/utils/util.js` - 更新为使用 TimeUtils

### 组件文件
- `components/time-modify-modal/time-modify-modal.js` - 修复时间选择器格式化问题

### 页面文件
- `pages/complaint/detail.js` - 统一时间格式化
- `pages/complaint/detail.wxml` - 更新 WXS 时间处理
- `pages/complaint/list.js` - 统一时间格式化
- `pages/complaint/list.wxml` - 更新 WXS 时间处理
- `pages/activity/detail.js` - 统一时间格式化
- `pages/photoWall/detail.js` - 统一时间格式化

## 最佳实践

### 1. 统一使用 TimeUtils

```javascript
// ✅ 推荐
import TimeUtils from '../../common/TimeUtils.js';
const formattedTime = TimeUtils.formatTime(date);

// ❌ 不推荐
const date = new Date(timestamp);
const formattedTime = `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
```

### 2. 输入验证

```javascript
// ✅ 推荐
if (!TimeUtils.isValidDate(userInput)) {
  wx.showToast({ title: '请输入正确的日期格式', icon: 'none' });
  return;
}

// ❌ 不推荐
const date = new Date(userInput);
if (isNaN(date.getTime())) {
  // 处理错误
}
```

### 3. 错误处理

```javascript
// ✅ 推荐
const formattedTime = TimeUtils.formatTime(input);
if (!formattedTime) {
  console.error('时间格式化失败:', input);
  return '时间格式错误';
}

// ❌ 不推荐
try {
  const date = new Date(input);
  return date.toLocaleString();
} catch (error) {
  return '';
}
```

## 测试

运行测试文件验证时间处理是否正常：

```bash
node test/time-format-test.js
```

## 注意事项

1. **时间戳格式**: TimeUtils 自动识别秒和毫秒时间戳（< 10000000000 为秒）
2. **时区处理**: 所有时间都按本地时区处理
3. **性能考虑**: TimeUtils 方法都经过优化，可以安全地在循环中使用
4. **向后兼容**: 现有代码可以逐步迁移到 TimeUtils，不会破坏现有功能

## 后续计划

1. 逐步将项目中所有时间处理代码迁移到 TimeUtils
2. 添加更多时间处理功能（如时区转换、国际化等）
3. 考虑将 TimeUtils 发布为独立的工具包
