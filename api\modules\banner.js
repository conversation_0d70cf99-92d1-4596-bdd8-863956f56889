import request, { analysisRes } from "../request";
import config from "../config";
import requestManager from "../../utils/requestManager.js";

const { banner } = config.apiUrls;

export default {
  // 获取轮播图列表
  async getList() {
    return requestManager.request(
      banner.list,
      {},
      async () => {
        const res = await request.get(banner.list);
        return analysisRes(res);
      },
      { cacheTime: 300000 } // 缓存5分钟，轮播图变化很少
    );
  },
};
