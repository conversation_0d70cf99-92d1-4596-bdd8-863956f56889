import siteinfo from '../../siteinfo.js';
import serviceApi from '../../api/modules/service.js';

Page({
  data: {
    userInfo: null,
    siteinfo,
    pets: null,
    typesList: [],
    allTypesList: [], // 完整的服务类型列表
    typeId: null,
    currentTabCode: null,
    currentType: {},
    serviceList: [],
    selectedEmployee: null, // 选中的员工信息
    employeeServiceTypes: [], // 员工可服务的类型
    showEmployeeNotice: false, // 是否显示员工提示
    isInitialized: false, // 标记是否已初始化
    serviceListLoading: false, // 标记服务列表是否正在加载
  },

  onLoad(options) {
    const { data, employeeId, fromNav } = options;

    // 如果是从底部导航进入，清除指定员工信息
    if (fromNav === 'true') {
      this.clearSelectedEmployee();
    }

    this.setData({
      currentTabCode: data,
      employeeId: employeeId || null, // 保存员工ID
    });

    // 如果是从员工选择页面跳转过来的，处理员工信息
    if (employeeId && !fromNav) {
      this.handleSelectedEmployee();
    }
  },

  onShow() {
    const item = wx.getStorageSync('selectPetInfo');
    this.setData({
      pets: item,
    });

    // 检查是否有选中的员工（可能从其他页面返回）
    this.checkSelectedEmployee();

    // 只有在已初始化且宠物信息发生变化时才重新加载服务列表
    if (this.data.isInitialized && this.data.typeId) {
      // 检查宠物信息是否发生变化
      const currentPets = this.data.pets;
      const newPets = item;

      // 简单比较宠物信息是否变化（可以根据需要调整比较逻辑）
      const petsChanged = JSON.stringify(currentPets) !== JSON.stringify(newPets);

      if (petsChanged) {
        console.log('宠物信息发生变化，重新加载服务列表');
        this.getServiceList(this.data.typeId);
      }
    }
  },

  /**
   * 处理选中的员工信息
   */
  handleSelectedEmployee() {
    const selectedEmployee = wx.getStorageSync('selectedEmployee');
    if (selectedEmployee) {
      console.log('选中的员工信息:', selectedEmployee);

      this.setData({
        selectedEmployee,
        showEmployeeNotice: true
      });

      // 提取员工可服务的类型
      this.extractEmployeeServiceTypes(selectedEmployee);

      wx.showToast({
        title: `已选择${selectedEmployee.name}师傅`,
        icon: 'success',
        duration: 2000
      });
    }
  },

  /**
   * 检查是否有选中的员工
   */
  checkSelectedEmployee() {
    const selectedEmployee = wx.getStorageSync('selectedEmployee');
    if (selectedEmployee) {
      this.setData({
        selectedEmployee,
        showEmployeeNotice: true
      });
      this.extractEmployeeServiceTypes(selectedEmployee);
    } else {
      this.setData({
        selectedEmployee: null,
        showEmployeeNotice: false,
        employeeServiceTypes: [],
        employeeServiceTypeNames: []
      });
    }
  },

  /**
   * 提取员工可服务的类型
   */
  extractEmployeeServiceTypes(employee) {
    // 员工数据中可能的服务类型字段：serviceTypes, serviceTypeIds, availableServiceTypes 等
    const serviceTypes = employee.serviceTypes || employee.serviceTypeIds || employee.availableServiceTypes || [];
    const serviceTypeNames = employee.serviceTypeNames || [];

    console.log('员工可服务类型ID:', serviceTypes);
    console.log('员工可服务类型名称:', serviceTypeNames);
    console.log('当前所有服务类型:', this.data.allTypesList);

    this.setData({
      employeeServiceTypes: serviceTypes,
      employeeServiceTypeNames: serviceTypeNames
    });

    // 如果当前有服务类型列表，需要重新过滤
    if (this.data.allTypesList.length > 0) {
      const filteredTypes = this.filterTypesByEmployee(this.data.allTypesList);
      console.log('过滤后的服务类型:', filteredTypes);

      const newTypeId = filteredTypes[0]?.id || null;
      const currentTypeId = this.data.typeId;

      this.setData({
        typesList: filteredTypes,
        typeId: newTypeId,
        currentType: filteredTypes[0] || {},
      });

      // 只有当类型ID发生变化时才重新加载服务列表
      if (newTypeId !== currentTypeId) {
        this.getServiceList(newTypeId);
      }
    }
  },

  /**
   * 根据员工可服务类型过滤服务类型列表
   */
  filterTypesByEmployee(typesList) {
    const { employeeServiceTypes, employeeServiceTypeNames } = this.data;

    // 如果没有员工服务类型限制，返回所有类型
    if ((!employeeServiceTypes || employeeServiceTypes.length === 0) &&
        (!employeeServiceTypeNames || employeeServiceTypeNames.length === 0)) {
      return typesList;
    }

    // 优先使用服务类型名称匹配
    if (employeeServiceTypeNames && employeeServiceTypeNames.length > 0) {
      return typesList.filter(type => {
        // 检查服务类型的subtype是否在员工可服务类型名称中
        return employeeServiceTypeNames.includes(type.subtype);
      });
    }

    // 如果没有名称匹配，使用ID匹配
    if (employeeServiceTypes && employeeServiceTypes.length > 0) {
      return typesList.filter(type =>
        employeeServiceTypes.includes(type.id)
      );
    }

    return typesList;
  },

  /**
   * 清除选中的员工信息
   */
  clearSelectedEmployee() {
    wx.removeStorageSync('selectedEmployee');
    this.setData({
      selectedEmployee: null,
      showEmployeeNotice: false,
      employeeServiceTypes: [],
      employeeServiceTypeNames: []
    });
  },

  /**
   * 手动清除员工选择
   */
  onClearEmployee() {
    this.clearSelectedEmployee();

    // 恢复完整的服务类型列表
    if (this.data.allTypesList.length > 0) {
      this.setData({
        typesList: this.data.allTypesList,
        typeId: this.data.allTypesList[0]?.id || null,
        currentType: this.data.allTypesList[0] || {},
      });
      // 重新加载服务列表
      this.getServiceList(this.data.allTypesList[0]?.id || null);
    } else {
      // 如果没有缓存的完整列表，重新获取
      this.getTypeList(this.data.currentTabCode);
    }

    wx.showToast({
      title: '已切换为系统派单',
      icon: 'success',
      duration: 1500
    });
  },

  onShareAppMessage: function () {
    return {
      title: '贝宠约洗，足不出户享精致洗护～',
      path: `/pages/index/index?aUserId=${this.data?.userInfo?.id || ''}&shareCode=${
        this.data?.userInfo?.promotionCode || ''
      }&shareTime=${Date.now()}`,
      imageUrl: 'https://xian7.zos.ctyun.cn/pet/static/share.png',
    };
  },

  onShareTimeline: function () {
    return {
      title: '「忙到没空送洗？上门服务解忧！」贝宠约洗，专业到家超省心～',
      imageUrl: 'https://xian7.zos.ctyun.cn/pet/static/share1.png',
      query: `aUserId=${this.data?.userInfo?.id || ''}&shareCode=${
        this.data?.userInfo?.promotionCode || ''
      }&shareTime=${Date.now()}`,
    };
  },

  addCurrentPet() {
    const userInfo = this.data.userInfo;
    if (!userInfo) {
      wx.navigateTo({
        url: '/pages/login/index',
      });
      return;
    }
    wx.navigateTo({
      url: '/pages/service/pet/index?isService=true',
    });
  },

  // 监听导航栏切换事件
  handleTabChange(e) {
    const { code } = e.detail;
    console.log('导航栏切换事件触发:', code);
    this.getTypeList(code);
    this.setData({
      isInitialized: true
    });
  },

  // 页面准备就绪后的初始化
  onReady() {
    // 如果导航栏组件还没有触发 tabchange 事件，这里作为备用初始化
    setTimeout(() => {
      if (!this.data.isInitialized && (!this.data.typesList || this.data.typesList.length === 0)) {
        // 如果还没有数据，尝试使用当前选中的标签代码或默认值
        const code = this.data.currentTabCode;
        if (code) {
          console.log('备用初始化触发:', code);
          this.getTypeList(code);
          this.setData({
            isInitialized: true
          });
        }
      }
    }, 500); // 给导航栏组件一些时间来触发事件
  },

  // 获取一级类目
  getTypeList(type) {
    if (!type) return;
    serviceApi.list(type).then(res => {
      let typesList = (res.list || []).map(item => ({
        id: item.id,
        avatar: item.avatar,
        type: item.name.slice(0, -2),
        subtype: item.name.slice(-2),
      }));

      // 保存完整的服务类型列表
      this.setData({
        allTypesList: typesList
      });

      // 如果有选中的员工，过滤服务类型
      typesList = this.filterTypesByEmployee(typesList);

      this.setData({
        typesList,
        typeId: typesList[0]?.id || null,
        currentType: typesList[0] || {},
      });
      this.getServiceList(typesList[0]?.id || null);
    }).catch(err => {
      console.error('获取服务类目失败:', err);
      // 设置空数据，避免界面异常
      this.setData({
        typesList: [],
        typeId: null,
        currentType: {},
        serviceList: []
      });
    });
  },

  // 获取完整的服务类型列表（用于清除员工选择时）
  getServiceTypes() {
    const type = this.data.currentTabCode;
    if (!type) return;

    serviceApi.list(type).then(res => {
      const typesList = (res.list || []).map(item => ({
        id: item.id,
        avatar: item.avatar,
        type: item.name.slice(0, -2),
        subtype: item.name.slice(-2),
      }));

      // 保存完整列表
      this.setData({
        allTypesList: typesList,
        typesList,
        typeId: typesList[0]?.id || null,
        currentType: typesList[0] || {},
      });
      this.getServiceList(typesList[0]?.id || null);
    }).catch(err => {
      console.error('获取服务类目失败:', err);
    });
  },

  // 一级类目切换
  changeType(evt) {
    let { id } = evt.currentTarget.dataset;
    if (id == this.data.typeId) return;
    this.setData({
      typeId: id,
      currentType: this.data.typesList.find(item => item.id == id) || {},
    });
    this.getServiceList(id);
  },

  // 获取服务列表
  getServiceList(typeId) {
    // 如果 typeId 为空，则不发起请求，避免无效的 API 调用
    if (!typeId) {
      this.setData({
        serviceList: [],
        serviceListLoading: false,
      });
      return;
    }

    // 防止重复请求
    if (this.data.serviceListLoading) {
      console.log('服务列表正在加载中，跳过重复请求');
      return;
    }

    console.log('开始加载服务列表:', typeId);
    this.setData({
      serviceListLoading: true
    });

    const { type, hairType, weightType } = this.data.pets || {};
    let petInfo = {};
    if (type) {
      petInfo['type'] = type;
    }
    if (hairType) {
      petInfo['hairType'] = hairType;
    }
    if (weightType) {
      petInfo['weightType'] = weightType;
    }

    serviceApi.services(typeId, petInfo).then(list => {
      this.setData({
        serviceList: list || [],
        serviceListLoading: false,
      });
      console.log('服务列表加载完成');
    }).catch(err => {
      console.error('获取服务列表失败:', err);
      this.setData({
        serviceList: [],
        serviceListLoading: false,
      });
    });
  },

  redirect(evt) {
    let { item } = evt.currentTarget.dataset;
    wx.setStorageSync('selectServiceInfo', {
      ...item,
      currentType: this.data.currentType,
    });
    wx.navigateTo({
      url: '/pages/service/reservation/index?serviceId=' + item.id,
    });
  },
});
