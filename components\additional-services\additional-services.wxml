<!-- 追加服务卡片 -->
<view wx:if="{{additionalServices.length > 0}}" class="additional-services-section">
  <view class="section-title">追加服务</view>
  <view class="additional-services-list">
    <view wx:for="{{additionalServices}}" wx:key="id" class="additional-service-card">
      <!-- 服务头部信息 -->
      <view class="service-header">
        <view class="service-info">
          <text class="service-name">
            <!-- 根据服务类型显示不同的名称 -->
            <block wx:if="{{item.type === 'original'}}">
              {{item.name || '增项服务'}}
            </block>
            <block wx:else>
            </block>
          </text>
          <text class="service-time">{{item.createdAt}}</text>
        </view>
        <view class="service-status" style="color: {{item.statusInfo.color}}">
          {{item.statusInfo.text}}
        </view>
      </view>

      <!-- 服务详细信息 -->
      <block wx:if="{{item.type !== 'original' && item.details && item.details.length > 0}}">
        <!-- 追加服务详细显示 -->
        <view class="service-details">
          <view wx:for="{{item.originalDetails}}" wx:for-item="detail" wx:key="detail.id" class="service-detail-item">
            <view class="detail-info">
              <text class="detail-name">{{detail.serviceName}}</text>
              <text class="detail-quantity">×{{detail.quantity}}</text>
            </view>
            <text class="detail-price">¥{{detail.servicePrice}}</text>
          </view>
        </view>
      </block>

      <!-- 服务价格信息 -->
      <view class="service-price">
        <block wx:if="{{item.type === 'original'}}">
          <!-- 原始增项服务显示 -->
          <view class="price-item">
            <text class="price-label">服务价格：</text>
            <text class="price-value">¥{{item.price || item.totalFee}}</text>
          </view>
          <view class="price-item">
            <text class="price-value" style="color: #52c41a; font-weight: normal;">已包含在主订单</text>
          </view>
        </block>
        <block wx:else>
          <!-- 追加服务订单显示 -->
          <view class="price-item">
            <text class="price-label">原价：</text>
            <text class="price-value">¥{{item.originalPrice}}</text>
          </view>
          <view class="price-item">
            <text class="price-label">实付：</text>
            <text class="price-value highlight">¥{{item.totalFee}}</text>
          </view>
        </block>
      </view>

      <!-- 操作按钮 -->
      <view class="service-actions" wx:if="{{item.type !== 'original'}}">
        <view wx:for="{{item.statusInfo.actions}}" wx:for-item="action" wx:key="*this" class="action-btn-small">
          <view wx:if="{{action === 'view'}}"
                class="btn-outline"
                bindtap="onViewDetail"
                data-id="{{item.id}}">
            查看
          </view>
          <view wx:elif="{{action === 'pay'}}"
                class="btn-primary"
                bindtap="onPayService"
                data-id="{{item.id}}">
            付款
          </view>
          <view wx:elif="{{action === 'delete'}}"
                class="btn-danger"
                bindtap="onDeleteService"
                data-id="{{item.id}}"
                data-name="{{item.type === 'original' ? (item.name || '增项服务') : (item.details && item.details.length > 0 ? item.details[0].serviceName : (item.serviceName || item.sn || '追加服务'))}}">
            删除
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
