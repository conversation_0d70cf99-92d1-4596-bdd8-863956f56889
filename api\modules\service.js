import request, { analysisRes } from '../request';
import config from '../config';

const { service } = config.apiUrls;
export default {
  /**
   * 获取服务类目列表
   * @param {string} type 类型，从字典获取，如约洗护，约美容
   * @returns any[]
   */
  async list(type) {
    const res = await request.get(service.list, { type });
    return analysisRes(res);
  },

  // services: "/openapi/services/{typeId}", // 获取指定类目下的服务列表
  /**
   * 获取指定类目下的服务列表
   * @param {string} typeId 类型id
   * @param {object} [petInfo] 宠物信息
   * @param {string} petInfo.type 宠物类型，从字典获取，猫，狗
   * @param {string} petInfo.hairType 宠物毛发类型，从字典获取，长毛，短毛
   * @param {string} petInfo.weightType 宠物体重分类
   * @returns any[]
   */
  async services(typeId, petInfo = {}) {
    console.log(`[API] 请求服务列表 - typeId: ${typeId}, petInfo:`, petInfo);
    const res = await request.get(service.services.replace('{typeId}', typeId), petInfo);
    const result = analysisRes(res);
    console.log(`[API] 服务列表响应 - typeId: ${typeId}, 结果数量: ${result?.length || 0}`);
    return result;
  },

  // additionalService: "/openapi/service/additional-service/{serviceId}", // 获取增项服务
  /**
   * 获取增项服务
   * @param {string} serviceId 服务id
   * @returns any[]
   */
  async getAdditionalService(serviceId) {
    const res = await request.get(service.additionalService.replace('{serviceId}', serviceId));
    return analysisRes(res);
  },
};
