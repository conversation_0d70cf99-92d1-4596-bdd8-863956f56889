/* 个人信息编辑弹窗样式 */
.profile-edit-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: relative;
  width: 680rpx;
  max-height: 80vh;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 40rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 48rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  flex: 1;
  padding: 40rpx;
  overflow-y: auto;
}

.form-section {
  margin-bottom: 40rpx;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

/* 头像编辑样式 */
.avatar-edit-wrapper {
  margin: 20rpx 0;
}

.avatar-display {
  display: flex;
  justify-content: center;
  margin-bottom: 24rpx;
}

.avatar-container {
  width: 140rpx;
  height: 140rpx;
  border-radius: 50%;
  overflow: hidden;
  border: 3rpx solid #e9ecef;
  cursor: pointer;
  transition: all 0.3s ease;
}

.avatar-container:active {
  transform: scale(0.95);
  border-color: #007aff;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-actions {
  display: flex;
  justify-content: center;
}

.avatar-edit-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  height: 64rpx;
  background-color: #f8f9fa;
  color: #666;
  border: 1rpx solid #e9ecef;
  border-radius: 32rpx;
  font-size: 26rpx;
  transition: all 0.3s ease;
  padding: 0 32rpx;
  min-width: 200rpx;
}

.avatar-edit-btn:active {
  background-color: #007aff;
  color: #fff;
  border-color: #007aff;
}

.btn-icon {
  font-size: 28rpx;
}

.btn-text {
  font-size: 26rpx;
}

/* 输入框样式 */
.input-wrapper {
  position: relative;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  background-color: #fff;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #007aff;
  outline: none;
}

.char-count {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 24rpx;
  color: #999;
}

.error-tip {
  margin-top: 10rpx;
  font-size: 24rpx;
  color: #ff4757;
}

/* 只读字段样式 */
.readonly-field {
  height: 80rpx;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border: 1rpx solid #e9ecef;
}

.readonly-text {
  font-size: 28rpx;
  color: #666;
}

/* 底部按钮样式 */
.modal-footer {
  display: flex;
  padding: 30rpx 40rpx 40rpx;
  gap: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-cancel {
  background-color: #f8f9fa;
  color: #666;
}

.btn-cancel:active {
  background-color: #e9ecef;
}

.btn-confirm {
  background-color: #007aff;
  color: #fff;
}

.btn-confirm:active {
  background-color: #0056b3;
}

.btn-confirm.loading {
  background-color: #ccc;
  color: #999;
}

.btn:disabled {
  background-color: #ccc;
  color: #999;
  cursor: not-allowed;
}
